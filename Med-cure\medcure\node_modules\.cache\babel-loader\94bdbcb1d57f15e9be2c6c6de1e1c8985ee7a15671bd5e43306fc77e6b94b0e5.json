{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\components\\\\AuthModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Login from './Login';\nimport Signup from './Signup';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthModal = ({\n  isOpen,\n  onClose\n}) => {\n  _s();\n  const [currentView, setCurrentView] = useState('login'); // 'login' or 'signup'\n\n  if (!isOpen) return null;\n  const handleSwitchToSignup = () => {\n    setCurrentView('signup');\n  };\n  const handleSwitchToLogin = () => {\n    setCurrentView('login');\n  };\n  const handleClose = () => {\n    setCurrentView('login'); // Reset to login view\n    onClose();\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-modal\",\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"auth-close-button\",\n        onClick: handleClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), currentView === 'login' ? /*#__PURE__*/_jsxDEV(Login, {\n        onSwitchToSignup: handleSwitchToSignup,\n        onClose: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Signup, {\n        onSwitchToLogin: handleSwitchToLogin,\n        onClose: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthModal, \"ZYqBjaZy1tIwqH2X87I72B8vfaM=\");\n_c = AuthModal;\nexport default AuthModal;\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Signup", "jsxDEV", "_jsxDEV", "AuthModal", "isOpen", "onClose", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "handleSwitchToSignup", "handleSwitchToLogin", "handleClose", "handleBackdropClick", "e", "target", "currentTarget", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSwitchToSignup", "onSwitchToLogin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/components/AuthModal.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Login from './Login';\nimport Signup from './Signup';\nimport './Auth.css';\n\nconst AuthModal = ({ isOpen, onClose }) => {\n  const [currentView, setCurrentView] = useState('login'); // 'login' or 'signup'\n\n  if (!isOpen) return null;\n\n  const handleSwitchToSignup = () => {\n    setCurrentView('signup');\n  };\n\n  const handleSwitchToLogin = () => {\n    setCurrentView('login');\n  };\n\n  const handleClose = () => {\n    setCurrentView('login'); // Reset to login view\n    onClose();\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  return (\n    <div className=\"auth-modal\" onClick={handleBackdropClick}>\n      <div className=\"auth-modal-content\">\n        <button className=\"auth-close-button\" onClick={handleClose}>\n          ×\n        </button>\n        \n        {currentView === 'login' ? (\n          <Login \n            onSwitchToSignup={handleSwitchToSignup}\n            onClose={handleClose}\n          />\n        ) : (\n          <Signup \n            onSwitchToLogin={handleSwitchToLogin}\n            onClose={handleClose}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEzD,IAAI,CAACM,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCD,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChCF,cAAc,CAAC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxBH,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;IACzBH,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMO,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCJ,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACET,OAAA;IAAKc,SAAS,EAAC,YAAY;IAACC,OAAO,EAAEL,mBAAoB;IAAAM,QAAA,eACvDhB,OAAA;MAAKc,SAAS,EAAC,oBAAoB;MAAAE,QAAA,gBACjChB,OAAA;QAAQc,SAAS,EAAC,mBAAmB;QAACC,OAAO,EAAEN,WAAY;QAAAO,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERf,WAAW,KAAK,OAAO,gBACtBL,OAAA,CAACH,KAAK;QACJwB,gBAAgB,EAAEd,oBAAqB;QACvCJ,OAAO,EAAEM;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,gBAEFpB,OAAA,CAACF,MAAM;QACLwB,eAAe,EAAEd,mBAAoB;QACrCL,OAAO,EAAEM;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA7CIH,SAAS;AAAAsB,EAAA,GAATtB,SAAS;AA+Cf,eAAeA,SAAS;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}