{"ast": null, "code": "/**\n * Split this component for RSC import\n */\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_MODE_STORAGE_KEY = 'mode';\nexport const DEFAULT_COLOR_SCHEME_STORAGE_KEY = 'color-scheme';\nexport const DEFAULT_ATTRIBUTE = 'data-color-scheme';\nexport default function InitColorSchemeScript(options) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = 'light',\n    defaultDarkColorScheme = 'dark',\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    attribute: initialAttribute = DEFAULT_ATTRIBUTE,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = options || {};\n  let setter = '';\n  let attribute = initialAttribute;\n  if (initialAttribute === 'class') {\n    attribute = '.%s';\n  }\n  if (initialAttribute === 'data') {\n    attribute = '[data-%s]';\n  }\n  if (attribute.startsWith('.')) {\n    const selector = attribute.substring(1);\n    setter += `${colorSchemeNode}.classList.remove('${selector}'.replace('%s', light), '${selector}'.replace('%s', dark));\n      ${colorSchemeNode}.classList.add('${selector}'.replace('%s', colorScheme));`;\n  }\n  const matches = attribute.match(/\\[([^\\]]+)\\]/); // case [data-color-scheme='%s'] or [data-color-scheme]\n  if (matches) {\n    const [attr, value] = matches[1].split('=');\n    if (!value) {\n      setter += `${colorSchemeNode}.removeAttribute('${attr}'.replace('%s', light));\n      ${colorSchemeNode}.removeAttribute('${attr}'.replace('%s', dark));`;\n    }\n    setter += `\n      ${colorSchemeNode}.setAttribute('${attr}'.replace('%s', colorScheme), ${value ? `${value}.replace('%s', colorScheme)` : '\"\"'});`;\n  } else {\n    setter += `${colorSchemeNode}.setAttribute('${attribute}', colorScheme);`;\n  }\n  return /*#__PURE__*/_jsx(\"script\", {\n    suppressHydrationWarning: true,\n    nonce: typeof window === 'undefined' ? nonce : ''\n    // eslint-disable-next-line react/no-danger\n    ,\n\n    dangerouslySetInnerHTML: {\n      __html: `(function() {\ntry {\n  let colorScheme = '';\n  const mode = localStorage.getItem('${modeStorageKey}') || '${defaultMode}';\n  const dark = localStorage.getItem('${colorSchemeStorageKey}-dark') || '${defaultDarkColorScheme}';\n  const light = localStorage.getItem('${colorSchemeStorageKey}-light') || '${defaultLightColorScheme}';\n  if (mode === 'system') {\n    // handle system mode\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      colorScheme = dark\n    } else {\n      colorScheme = light\n    }\n  }\n  if (mode === 'light') {\n    colorScheme = light;\n  }\n  if (mode === 'dark') {\n    colorScheme = dark;\n  }\n  if (colorScheme) {\n    ${setter}\n  }\n} catch(e){}})();`\n    }\n  }, \"mui-color-scheme-init\");\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "DEFAULT_MODE_STORAGE_KEY", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "DEFAULT_ATTRIBUTE", "InitColorSchemeScript", "options", "defaultMode", "defaultLightColorScheme", "defaultDarkColorScheme", "modeStorageKey", "colorSchemeStorageKey", "attribute", "initialAttribute", "colorSchemeNode", "nonce", "setter", "startsWith", "selector", "substring", "matches", "match", "attr", "value", "split", "suppressHydrationWarning", "window", "dangerouslySetInnerHTML", "__html"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/InitColorSchemeScript/InitColorSchemeScript.js"], "sourcesContent": ["/**\n * Split this component for RSC import\n */\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_MODE_STORAGE_KEY = 'mode';\nexport const DEFAULT_COLOR_SCHEME_STORAGE_KEY = 'color-scheme';\nexport const DEFAULT_ATTRIBUTE = 'data-color-scheme';\nexport default function InitColorSchemeScript(options) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = 'light',\n    defaultDarkColorScheme = 'dark',\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    attribute: initialAttribute = DEFAULT_ATTRIBUTE,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = options || {};\n  let setter = '';\n  let attribute = initialAttribute;\n  if (initialAttribute === 'class') {\n    attribute = '.%s';\n  }\n  if (initialAttribute === 'data') {\n    attribute = '[data-%s]';\n  }\n  if (attribute.startsWith('.')) {\n    const selector = attribute.substring(1);\n    setter += `${colorSchemeNode}.classList.remove('${selector}'.replace('%s', light), '${selector}'.replace('%s', dark));\n      ${colorSchemeNode}.classList.add('${selector}'.replace('%s', colorScheme));`;\n  }\n  const matches = attribute.match(/\\[([^\\]]+)\\]/); // case [data-color-scheme='%s'] or [data-color-scheme]\n  if (matches) {\n    const [attr, value] = matches[1].split('=');\n    if (!value) {\n      setter += `${colorSchemeNode}.removeAttribute('${attr}'.replace('%s', light));\n      ${colorSchemeNode}.removeAttribute('${attr}'.replace('%s', dark));`;\n    }\n    setter += `\n      ${colorSchemeNode}.setAttribute('${attr}'.replace('%s', colorScheme), ${value ? `${value}.replace('%s', colorScheme)` : '\"\"'});`;\n  } else {\n    setter += `${colorSchemeNode}.setAttribute('${attribute}', colorScheme);`;\n  }\n  return /*#__PURE__*/_jsx(\"script\", {\n    suppressHydrationWarning: true,\n    nonce: typeof window === 'undefined' ? nonce : ''\n    // eslint-disable-next-line react/no-danger\n    ,\n    dangerouslySetInnerHTML: {\n      __html: `(function() {\ntry {\n  let colorScheme = '';\n  const mode = localStorage.getItem('${modeStorageKey}') || '${defaultMode}';\n  const dark = localStorage.getItem('${colorSchemeStorageKey}-dark') || '${defaultDarkColorScheme}';\n  const light = localStorage.getItem('${colorSchemeStorageKey}-light') || '${defaultLightColorScheme}';\n  if (mode === 'system') {\n    // handle system mode\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      colorScheme = dark\n    } else {\n      colorScheme = light\n    }\n  }\n  if (mode === 'light') {\n    colorScheme = light;\n  }\n  if (mode === 'dark') {\n    colorScheme = dark;\n  }\n  if (colorScheme) {\n    ${setter}\n  }\n} catch(e){}})();`\n    }\n  }, \"mui-color-scheme-init\");\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,wBAAwB,GAAG,MAAM;AAC9C,OAAO,MAAMC,gCAAgC,GAAG,cAAc;AAC9D,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,QAAQ;IACtBC,uBAAuB,GAAG,OAAO;IACjCC,sBAAsB,GAAG,MAAM;IAC/BC,cAAc,GAAGR,wBAAwB;IACzCS,qBAAqB,GAAGR,gCAAgC;IACxDS,SAAS,EAAEC,gBAAgB,GAAGT,iBAAiB;IAC/CU,eAAe,GAAG,0BAA0B;IAC5CC;EACF,CAAC,GAAGT,OAAO,IAAI,CAAC,CAAC;EACjB,IAAIU,MAAM,GAAG,EAAE;EACf,IAAIJ,SAAS,GAAGC,gBAAgB;EAChC,IAAIA,gBAAgB,KAAK,OAAO,EAAE;IAChCD,SAAS,GAAG,KAAK;EACnB;EACA,IAAIC,gBAAgB,KAAK,MAAM,EAAE;IAC/BD,SAAS,GAAG,WAAW;EACzB;EACA,IAAIA,SAAS,CAACK,UAAU,CAAC,GAAG,CAAC,EAAE;IAC7B,MAAMC,QAAQ,GAAGN,SAAS,CAACO,SAAS,CAAC,CAAC,CAAC;IACvCH,MAAM,IAAI,GAAGF,eAAe,sBAAsBI,QAAQ,4BAA4BA,QAAQ;AAClG,QAAQJ,eAAe,mBAAmBI,QAAQ,gCAAgC;EAChF;EACA,MAAME,OAAO,GAAGR,SAAS,CAACS,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;EACjD,IAAID,OAAO,EAAE;IACX,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC;IAC3C,IAAI,CAACD,KAAK,EAAE;MACVP,MAAM,IAAI,GAAGF,eAAe,qBAAqBQ,IAAI;AAC3D,QAAQR,eAAe,qBAAqBQ,IAAI,yBAAyB;IACrE;IACAN,MAAM,IAAI;AACd,QAAQF,eAAe,kBAAkBQ,IAAI,iCAAiCC,KAAK,GAAG,GAAGA,KAAK,6BAA6B,GAAG,IAAI,IAAI;EACpI,CAAC,MAAM;IACLP,MAAM,IAAI,GAAGF,eAAe,kBAAkBF,SAAS,kBAAkB;EAC3E;EACA,OAAO,aAAaX,IAAI,CAAC,QAAQ,EAAE;IACjCwB,wBAAwB,EAAE,IAAI;IAC9BV,KAAK,EAAE,OAAOW,MAAM,KAAK,WAAW,GAAGX,KAAK,GAAG;IAC/C;IAAA;;IAEAY,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AACd;AACA;AACA,uCAAuClB,cAAc,UAAUH,WAAW;AAC1E,uCAAuCI,qBAAqB,eAAeF,sBAAsB;AACjG,wCAAwCE,qBAAqB,gBAAgBH,uBAAuB;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,MAAM;AACZ;AACA;IACI;EACF,CAAC,EAAE,uBAAuB,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}