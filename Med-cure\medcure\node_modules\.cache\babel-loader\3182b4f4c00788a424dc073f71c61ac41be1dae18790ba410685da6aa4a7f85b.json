{"ast": null, "code": "import preprocessStyles from \"./preprocessStyles.js\";\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// We need to pass an argument as `{ theme }` for PigmentCSS, but we don't want to\n// allocate more objects.\nconst arg = {\n  theme: undefined\n};\n\n/**\n * Memoize style function on theme.\n * Intended to be used in styled() calls that only need access to the theme.\n */\nexport default function unstable_memoTheme(styleFn) {\n  let lastValue;\n  let lastTheme;\n  return function styleMemoized(props) {\n    let value = lastValue;\n    if (value === undefined || props.theme !== lastTheme) {\n      arg.theme = props.theme;\n      value = preprocessStyles(styleFn(arg));\n      lastValue = value;\n      lastTheme = props.theme;\n    }\n    return value;\n  };\n}", "map": {"version": 3, "names": ["preprocessStyles", "arg", "theme", "undefined", "unstable_memoTheme", "styleFn", "lastValue", "lastTheme", "styleMemoized", "props", "value"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/memoTheme.js"], "sourcesContent": ["import preprocessStyles from \"./preprocessStyles.js\";\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// We need to pass an argument as `{ theme }` for PigmentCSS, but we don't want to\n// allocate more objects.\nconst arg = {\n  theme: undefined\n};\n\n/**\n * Memoize style function on theme.\n * Intended to be used in styled() calls that only need access to the theme.\n */\nexport default function unstable_memoTheme(styleFn) {\n  let lastValue;\n  let lastTheme;\n  return function styleMemoized(props) {\n    let value = lastValue;\n    if (value === undefined || props.theme !== lastTheme) {\n      arg.theme = props.theme;\n      value = preprocessStyles(styleFn(arg));\n      lastValue = value;\n      lastTheme = props.theme;\n    }\n    return value;\n  };\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;;AAEpD;;AAEA;AACA;AACA,MAAMC,GAAG,GAAG;EACVC,KAAK,EAAEC;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAClD,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;IACnC,IAAIC,KAAK,GAAGJ,SAAS;IACrB,IAAII,KAAK,KAAKP,SAAS,IAAIM,KAAK,CAACP,KAAK,KAAKK,SAAS,EAAE;MACpDN,GAAG,CAACC,KAAK,GAAGO,KAAK,CAACP,KAAK;MACvBQ,KAAK,GAAGV,gBAAgB,CAACK,OAAO,CAACJ,GAAG,CAAC,CAAC;MACtCK,SAAS,GAAGI,KAAK;MACjBH,SAAS,GAAGE,KAAK,CAACP,KAAK;IACzB;IACA,OAAOQ,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}