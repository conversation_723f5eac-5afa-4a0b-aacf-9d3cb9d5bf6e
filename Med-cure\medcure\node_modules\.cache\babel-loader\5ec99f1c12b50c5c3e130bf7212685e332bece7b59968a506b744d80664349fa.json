{"ast": null, "code": "export { default } from \"./createCssVarsProvider.js\";\nexport { default as prepareCssVars } from \"./prepareCssVars.js\";\nexport { default as prepareTypographyVars } from \"./prepareTypographyVars.js\";\nexport { default as createCssVarsTheme } from \"./createCssVarsTheme.js\";\nexport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";", "map": {"version": 3, "names": ["default", "prepareCssVars", "prepareTypographyVars", "createCssVarsTheme", "createGetColorSchemeSelector"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/cssVars/index.js"], "sourcesContent": ["export { default } from \"./createCssVarsProvider.js\";\nexport { default as prepareCssVars } from \"./prepareCssVars.js\";\nexport { default as prepareTypographyVars } from \"./prepareTypographyVars.js\";\nexport { default as createCssVarsTheme } from \"./createCssVarsTheme.js\";\nexport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,4BAA4B;AACpD,SAASA,OAAO,IAAIC,cAAc,QAAQ,qBAAqB;AAC/D,SAASD,OAAO,IAAIE,qBAAqB,QAAQ,4BAA4B;AAC7E,SAASF,OAAO,IAAIG,kBAAkB,QAAQ,yBAAyB;AACvE,SAASC,4BAA4B,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}