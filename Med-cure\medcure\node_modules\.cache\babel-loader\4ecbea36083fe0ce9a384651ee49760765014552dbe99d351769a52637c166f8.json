{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\RightScreen.js\";\nimport React from 'react';\nimport './RightScreen.css';\nimport WelcomeCard from './WelcomeCard';\nimport ActivityDashboard from './ActivityDashboard';\nimport RoleSelector from './RoleSelector';\nimport MedicalQuery from './MedicalQuery';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RightScreen() {\n  const userName = \"<PERSON><PERSON><PERSON>\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"RightScreen\",\n    children: [/*#__PURE__*/_jsxDEV(WelcomeCard, {\n      name: userName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ActivityDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoleSelector, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MedicalQuery, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_c = RightScreen;\nexport default RightScreen;\nvar _c;\n$RefreshReg$(_c, \"RightScreen\");", "map": {"version": 3, "names": ["React", "WelcomeCard", "ActivityDashboard", "RoleSelector", "Medical<PERSON><PERSON>y", "Footer", "jsxDEV", "_jsxDEV", "RightScreen", "userName", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/RightScreen.js"], "sourcesContent": ["import React from 'react'\r\nimport './RightScreen.css'\r\nimport WelcomeCard from './WelcomeCard'\r\nimport ActivityDashboard from './ActivityDashboard';\r\nimport RoleSelector from './RoleSelector';\r\nimport MedicalQuery from './MedicalQuery';\r\nimport Footer from './Footer';\r\n\r\nfunction RightScreen () {\r\n    const userName = \"<PERSON><PERSON><PERSON>\";\r\n  return (\r\n    <div className='RightScreen'>\r\n      <WelcomeCard name={userName} />\r\n      <ActivityDashboard />\r\n      <RoleSelector />\r\n      <MedicalQuery />\r\n      <Footer />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default RightScreen\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAC1B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,WAAWA,CAAA,EAAI;EACpB,MAAMC,QAAQ,GAAG,cAAc;EACjC,oBACEF,OAAA;IAAKG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BJ,OAAA,CAACN,WAAW;MAACW,IAAI,EAAEH;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/BT,OAAA,CAACL,iBAAiB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBT,OAAA,CAACJ,YAAY;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBT,OAAA,CAACH,YAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBT,OAAA,CAACF,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACC,EAAA,GAXQT,WAAW;AAapB,eAAeA,WAAW;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}