{"ast": null, "code": "/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultColorScheme=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(25)].map((_, index) => `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}overlays-${index}`), `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkBg`, `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkColor`];\nexport default excludeVariablesFromRoot;", "map": {"version": 3, "names": ["excludeVariablesFromRoot", "cssVarPrefix", "Array", "map", "_", "index"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/material/esm/styles/excludeVariablesFromRoot.js"], "sourcesContent": ["/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultColorScheme=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(25)].map((_, index) => `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}overlays-${index}`), `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkBg`, `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkColor`];\nexport default excludeVariablesFromRoot;"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,wBAAwB,GAAGC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK,KAAKJ,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,YAAYI,KAAK,EAAE,CAAC,EAAE,KAAKJ,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,uBAAuB,EAAE,KAAKA,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,0BAA0B,CAAC;AACjS,eAAeD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}