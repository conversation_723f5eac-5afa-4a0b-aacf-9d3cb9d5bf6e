{"ast": null, "code": "/**\n * The benefit of this function is to help developers get CSS var from theme without specifying the whole variable\n * and they does not need to remember the prefix (defined once).\n */\nexport default function createGetCssVar(prefix = '') {\n  function appendVar(...vars) {\n    if (!vars.length) {\n      return '';\n    }\n    const value = vars[0];\n    if (typeof value === 'string' && !value.match(/(#|\\(|\\)|(-?(\\d*\\.)?\\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\\d*\\.)?\\d+)$|(\\d+ \\d+ \\d+)/)) {\n      return `, var(--${prefix ? `${prefix}-` : ''}${value}${appendVar(...vars.slice(1))})`;\n    }\n    return `, ${value}`;\n  }\n\n  // AdditionalVars makes `getCssVar` less strict, so it can be use like this `getCssVar('non-mui-variable')` without type error.\n  const getCssVar = (field, ...fallbacks) => {\n    return `var(--${prefix ? `${prefix}-` : ''}${field}${appendVar(...fallbacks)})`;\n  };\n  return getCssVar;\n}", "map": {"version": 3, "names": ["createGetCssVar", "prefix", "appendVar", "vars", "length", "value", "match", "slice", "getCssVar", "field", "fallbacks"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/cssVars/createGetCssVar.js"], "sourcesContent": ["/**\n * The benefit of this function is to help developers get CSS var from theme without specifying the whole variable\n * and they does not need to remember the prefix (defined once).\n */\nexport default function createGetCssVar(prefix = '') {\n  function appendVar(...vars) {\n    if (!vars.length) {\n      return '';\n    }\n    const value = vars[0];\n    if (typeof value === 'string' && !value.match(/(#|\\(|\\)|(-?(\\d*\\.)?\\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\\d*\\.)?\\d+)$|(\\d+ \\d+ \\d+)/)) {\n      return `, var(--${prefix ? `${prefix}-` : ''}${value}${appendVar(...vars.slice(1))})`;\n    }\n    return `, ${value}`;\n  }\n\n  // AdditionalVars makes `getCssVar` less strict, so it can be use like this `getCssVar('non-mui-variable')` without type error.\n  const getCssVar = (field, ...fallbacks) => {\n    return `var(--${prefix ? `${prefix}-` : ''}${field}${appendVar(...fallbacks)})`;\n  };\n  return getCssVar;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,eAAeA,CAACC,MAAM,GAAG,EAAE,EAAE;EACnD,SAASC,SAASA,CAAC,GAAGC,IAAI,EAAE;IAC1B,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE;MAChB,OAAO,EAAE;IACX;IACA,MAAMC,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACrB,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,6GAA6G,CAAC,EAAE;MAC5J,OAAO,WAAWL,MAAM,GAAG,GAAGA,MAAM,GAAG,GAAG,EAAE,GAAGI,KAAK,GAAGH,SAAS,CAAC,GAAGC,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;IACvF;IACA,OAAO,KAAKF,KAAK,EAAE;EACrB;;EAEA;EACA,MAAMG,SAAS,GAAGA,CAACC,KAAK,EAAE,GAAGC,SAAS,KAAK;IACzC,OAAO,SAAST,MAAM,GAAG,GAAGA,MAAM,GAAG,GAAG,EAAE,GAAGQ,KAAK,GAAGP,SAAS,CAAC,GAAGQ,SAAS,CAAC,GAAG;EACjF,CAAC;EACD,OAAOF,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}