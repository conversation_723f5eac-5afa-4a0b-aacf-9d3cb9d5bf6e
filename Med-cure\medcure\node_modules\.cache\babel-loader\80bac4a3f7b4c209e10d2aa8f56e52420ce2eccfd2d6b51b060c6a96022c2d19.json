{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\ActivityDashboard.js\";\nimport React from 'react';\nimport './ActivityDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActivityCard = ({\n  title,\n  value,\n  subtitle,\n  icon\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"activity-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-title\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-value\",\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-subtitle\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 20\n    }, this), icon && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-icon\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 16\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = ActivityCard;\nfunction ActivityDashboard() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"dashboard-heading\",\n      children: \"Your Activity\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-container\",\n      children: [/*#__PURE__*/_jsxDEV(ActivityCard, {\n        title: \"Queries Today\",\n        value: \"3\",\n        subtitle: \"\\u2191 20% from yesterday\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActivityCard, {\n        title: \"Total Queries\",\n        value: \"12\",\n        subtitle: \"Medical Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActivityCard, {\n        title: \"Current Role\",\n        value: \"\",\n        subtitle: \"Doctor\",\n        icon: \"\\uD83E\\uDE7A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c2 = ActivityDashboard;\nexport default ActivityDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"ActivityCard\");\n$RefreshReg$(_c2, \"ActivityDashboard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ActivityCard", "title", "value", "subtitle", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ActivityDashboard", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/ActivityDashboard.js"], "sourcesContent": ["import React from 'react'\r\nimport './ActivityDashboard.css';\r\n\r\nconst ActivityCard = ({ title, value, subtitle, icon }) => {\r\n  return (\r\n    <div className=\"activity-card\">\r\n      <div className=\"card-title\">{title}</div>\r\n      <div className=\"card-value\">{value}</div>\r\n      {subtitle && <div className=\"card-subtitle\">{subtitle}</div>}\r\n      {icon && <div className=\"card-icon\">{icon}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction ActivityDashboard () {\r\n  return (\r\n    <div className=\"dashboard\">\r\n      <h2 className=\"dashboard-heading\">Your Activity</h2>\r\n      <div className=\"card-container\">\r\n        <ActivityCard\r\n          title=\"Queries Today\"\r\n          value=\"3\"\r\n          subtitle=\"↑ 20% from yesterday\"\r\n        />\r\n        <ActivityCard\r\n          title=\"Total Queries\"\r\n          value=\"12\"\r\n          subtitle=\"Medical Analysis\"\r\n        />\r\n        <ActivityCard\r\n          title=\"Current Role\"\r\n          value=\"\"\r\n          subtitle=\"Doctor\"\r\n          icon=\"🩺\"\r\n        />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default ActivityDashboard"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAK,CAAC,KAAK;EACzD,oBACEL,OAAA;IAAKM,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BP,OAAA;MAAKM,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAEL;IAAK;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACzCX,OAAA;MAAKM,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAEJ;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACxCP,QAAQ,iBAAIJ,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEH;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC3DN,IAAI,iBAAIL,OAAA;MAAKM,SAAS,EAAC,WAAW;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEV,CAAC;AAACC,EAAA,GATIX,YAAY;AAWlB,SAASY,iBAAiBA,CAAA,EAAI;EAC5B,oBACEb,OAAA;IAAKM,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBP,OAAA;MAAIM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpDX,OAAA;MAAKM,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BP,OAAA,CAACC,YAAY;QACXC,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAC,GAAG;QACTC,QAAQ,EAAC;MAAsB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACFX,OAAA,CAACC,YAAY;QACXC,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAC,IAAI;QACVC,QAAQ,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACFX,OAAA,CAACC,YAAY;QACXC,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAC,EAAE;QACRC,QAAQ,EAAC,QAAQ;QACjBC,IAAI,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACG,GAAA,GAxBQD,iBAAiB;AA0B1B,eAAeA,iBAAiB;AAAA,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}