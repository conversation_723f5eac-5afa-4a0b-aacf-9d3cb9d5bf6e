{"ast": null, "code": "export { default } from \"./breakpoints.js\";\nexport * from \"./breakpoints.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/breakpoints/index.js"], "sourcesContent": ["export { default } from \"./breakpoints.js\";\nexport * from \"./breakpoints.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}