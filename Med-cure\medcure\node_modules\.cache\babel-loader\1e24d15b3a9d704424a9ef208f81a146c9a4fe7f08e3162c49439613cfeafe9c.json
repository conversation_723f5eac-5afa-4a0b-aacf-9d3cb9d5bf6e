{"ast": null, "code": "import * as React from 'react';\nimport ThemeContext from \"./ThemeContext.js\";\nexport default function useTheme() {\n  const theme = React.useContext(ThemeContext);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- It's not required to run React.useDebugValue in production\n    React.useDebugValue(theme);\n  }\n  return theme;\n}", "map": {"version": 3, "names": ["React", "ThemeContext", "useTheme", "theme", "useContext", "process", "env", "NODE_ENV", "useDebugValue"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/private-theming/esm/useTheme/useTheme.js"], "sourcesContent": ["import * as React from 'react';\nimport ThemeContext from \"./ThemeContext.js\";\nexport default function useTheme() {\n  const theme = React.useContext(ThemeContext);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- It's not required to run React.useDebugValue in production\n    React.useDebugValue(theme);\n  }\n  return theme;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,YAAY,CAAC;EAC5C,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAP,KAAK,CAACQ,aAAa,CAACL,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}