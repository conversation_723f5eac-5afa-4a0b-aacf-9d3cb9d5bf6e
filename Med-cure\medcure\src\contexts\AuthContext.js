import React, { createContext, useContext, useState, useEffect } from 'react';
import apiService from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Check for existing user session on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('medcure_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      } catch (err) {
        console.error('Error parsing saved user data:', err);
        localStorage.removeItem('medcure_user');
      }
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    try {
      setError('');
      setLoading(true);
      
      const response = await apiService.login(username, password);
      
      if (response.authenticated) {
        const userData = {
          id: response.user_id,
          name: response.name,
          role: response.role,
          username: username
        };
        
        setUser(userData);
        localStorage.setItem('medcure_user', JSON.stringify(userData));
        return { success: true };
      } else {
        throw new Error('Authentication failed');
      }
    } catch (err) {
      const errorMessage = err.message || 'Login failed. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signup = async (userData) => {
    try {
      setError('');
      setLoading(true);
      
      const response = await apiService.signup(userData);
      
      if (response.success) {
        return { success: true, message: response.message };
      } else {
        throw new Error(response.message || 'Signup failed');
      }
    } catch (err) {
      const errorMessage = err.message || 'Signup failed. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setError('');
    localStorage.removeItem('medcure_user');
  };

  const updateUserProfile = async (profileData) => {
    try {
      setError('');
      const response = await apiService.updateProfile({
        user_id: user.id,
        ...profileData
      });
      
      if (response.success) {
        // Update local user data
        const updatedUser = { ...user, ...profileData };
        setUser(updatedUser);
        localStorage.setItem('medcure_user', JSON.stringify(updatedUser));
        return { success: true, message: response.message };
      }
    } catch (err) {
      const errorMessage = err.message || 'Profile update failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const changePassword = async (currentPassword, newPassword) => {
    try {
      setError('');
      const response = await apiService.changePassword({
        user_id: user.id,
        current_password: currentPassword,
        new_password: newPassword
      });
      
      if (response.success) {
        return { success: true, message: response.message };
      }
    } catch (err) {
      const errorMessage = err.message || 'Password change failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const value = {
    user,
    loading,
    error,
    login,
    signup,
    logout,
    updateUserProfile,
    changePassword,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
