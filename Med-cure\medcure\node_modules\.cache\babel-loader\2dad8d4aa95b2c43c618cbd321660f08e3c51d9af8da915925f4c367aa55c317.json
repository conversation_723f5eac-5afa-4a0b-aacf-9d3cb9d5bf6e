{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  // Check for existing user session on app load\n  useEffect(() => {\n    const savedUser = localStorage.getItem('medcure_user');\n    if (savedUser) {\n      try {\n        const userData = JSON.parse(savedUser);\n        setUser(userData);\n      } catch (err) {\n        console.error('Error parsing saved user data:', err);\n        localStorage.removeItem('medcure_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async (username, password) => {\n    try {\n      setError('');\n      setLoading(true);\n      const response = await apiService.login(username, password);\n      if (response.authenticated) {\n        const userData = {\n          id: response.user_id,\n          name: response.name,\n          role: response.role,\n          username: username\n        };\n        setUser(userData);\n        localStorage.setItem('medcure_user', JSON.stringify(userData));\n        return {\n          success: true\n        };\n      } else {\n        throw new Error('Authentication failed');\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Login failed. Please try again.';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const signup = async userData => {\n    try {\n      setError('');\n      setLoading(true);\n      const response = await apiService.signup(userData);\n      if (response.success) {\n        return {\n          success: true,\n          message: response.message\n        };\n      } else {\n        throw new Error(response.message || 'Signup failed');\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Signup failed. Please try again.';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setError('');\n    localStorage.removeItem('medcure_user');\n  };\n  const updateUserProfile = async profileData => {\n    try {\n      setError('');\n      const response = await apiService.updateProfile({\n        user_id: user.id,\n        ...profileData\n      });\n      if (response.success) {\n        // Update local user data\n        const updatedUser = {\n          ...user,\n          ...profileData\n        };\n        setUser(updatedUser);\n        localStorage.setItem('medcure_user', JSON.stringify(updatedUser));\n        return {\n          success: true,\n          message: response.message\n        };\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Profile update failed';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      setError('');\n      const response = await apiService.changePassword({\n        user_id: user.id,\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      if (response.success) {\n        return {\n          success: true,\n          message: response.message\n        };\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Password change failed';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    signup,\n    logout,\n    updateUserProfile,\n    changePassword,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"uhK5DVO1lnMu6N43kGsC7HYFAr0=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "error", "setError", "savedUser", "localStorage", "getItem", "userData", "JSON", "parse", "err", "console", "removeItem", "login", "username", "password", "response", "authenticated", "id", "user_id", "name", "role", "setItem", "stringify", "success", "errorMessage", "message", "signup", "logout", "updateUserProfile", "profileData", "updateProfile", "updatedUser", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  // Check for existing user session on app load\n  useEffect(() => {\n    const savedUser = localStorage.getItem('medcure_user');\n    if (savedUser) {\n      try {\n        const userData = JSON.parse(savedUser);\n        setUser(userData);\n      } catch (err) {\n        console.error('Error parsing saved user data:', err);\n        localStorage.removeItem('medcure_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (username, password) => {\n    try {\n      setError('');\n      setLoading(true);\n      \n      const response = await apiService.login(username, password);\n      \n      if (response.authenticated) {\n        const userData = {\n          id: response.user_id,\n          name: response.name,\n          role: response.role,\n          username: username\n        };\n        \n        setUser(userData);\n        localStorage.setItem('medcure_user', JSON.stringify(userData));\n        return { success: true };\n      } else {\n        throw new Error('Authentication failed');\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Login failed. Please try again.';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const signup = async (userData) => {\n    try {\n      setError('');\n      setLoading(true);\n      \n      const response = await apiService.signup(userData);\n      \n      if (response.success) {\n        return { success: true, message: response.message };\n      } else {\n        throw new Error(response.message || 'Signup failed');\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Signup failed. Please try again.';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setError('');\n    localStorage.removeItem('medcure_user');\n  };\n\n  const updateUserProfile = async (profileData) => {\n    try {\n      setError('');\n      const response = await apiService.updateProfile({\n        user_id: user.id,\n        ...profileData\n      });\n      \n      if (response.success) {\n        // Update local user data\n        const updatedUser = { ...user, ...profileData };\n        setUser(updatedUser);\n        localStorage.setItem('medcure_user', JSON.stringify(updatedUser));\n        return { success: true, message: response.message };\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Profile update failed';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      setError('');\n      const response = await apiService.changePassword({\n        user_id: user.id,\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      \n      if (response.success) {\n        return { success: true, message: response.message };\n      }\n    } catch (err) {\n      const errorMessage = err.message || 'Password change failed';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    signup,\n    logout,\n    updateUserProfile,\n    changePassword,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACtD,IAAIF,SAAS,EAAE;MACb,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QACtCL,OAAO,CAACQ,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZC,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEQ,GAAG,CAAC;QACpDL,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;MACzC;IACF;IACAX,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACFZ,QAAQ,CAAC,EAAE,CAAC;MACZF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMe,QAAQ,GAAG,MAAM7B,UAAU,CAAC0B,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAE3D,IAAIC,QAAQ,CAACC,aAAa,EAAE;QAC1B,MAAMV,QAAQ,GAAG;UACfW,EAAE,EAAEF,QAAQ,CAACG,OAAO;UACpBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI;UACnBC,IAAI,EAAEL,QAAQ,CAACK,IAAI;UACnBP,QAAQ,EAAEA;QACZ,CAAC;QAEDf,OAAO,CAACQ,QAAQ,CAAC;QACjBF,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEd,IAAI,CAACe,SAAS,CAAChB,QAAQ,CAAC,CAAC;QAC9D,OAAO;UAAEiB,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAI9B,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZ,MAAMe,YAAY,GAAGf,GAAG,CAACgB,OAAO,IAAI,iCAAiC;MACrEvB,QAAQ,CAACsB,YAAY,CAAC;MACtB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEtB,KAAK,EAAEuB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,MAAM,GAAG,MAAOpB,QAAQ,IAAK;IACjC,IAAI;MACFJ,QAAQ,CAAC,EAAE,CAAC;MACZF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMe,QAAQ,GAAG,MAAM7B,UAAU,CAACwC,MAAM,CAACpB,QAAQ,CAAC;MAElD,IAAIS,QAAQ,CAACQ,OAAO,EAAE;QACpB,OAAO;UAAEA,OAAO,EAAE,IAAI;UAAEE,OAAO,EAAEV,QAAQ,CAACU;QAAQ,CAAC;MACrD,CAAC,MAAM;QACL,MAAM,IAAIhC,KAAK,CAACsB,QAAQ,CAACU,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZ,MAAMe,YAAY,GAAGf,GAAG,CAACgB,OAAO,IAAI,kCAAkC;MACtEvB,QAAQ,CAACsB,YAAY,CAAC;MACtB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEtB,KAAK,EAAEuB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,MAAM,GAAGA,CAAA,KAAM;IACnB7B,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;EACzC,CAAC;EAED,MAAMiB,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF3B,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMa,QAAQ,GAAG,MAAM7B,UAAU,CAAC4C,aAAa,CAAC;QAC9CZ,OAAO,EAAErB,IAAI,CAACoB,EAAE;QAChB,GAAGY;MACL,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACQ,OAAO,EAAE;QACpB;QACA,MAAMQ,WAAW,GAAG;UAAE,GAAGlC,IAAI;UAAE,GAAGgC;QAAY,CAAC;QAC/C/B,OAAO,CAACiC,WAAW,CAAC;QACpB3B,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEd,IAAI,CAACe,SAAS,CAACS,WAAW,CAAC,CAAC;QACjE,OAAO;UAAER,OAAO,EAAE,IAAI;UAAEE,OAAO,EAAEV,QAAQ,CAACU;QAAQ,CAAC;MACrD;IACF,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZ,MAAMe,YAAY,GAAGf,GAAG,CAACgB,OAAO,IAAI,uBAAuB;MAC3DvB,QAAQ,CAACsB,YAAY,CAAC;MACtB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEtB,KAAK,EAAEuB;MAAa,CAAC;IAChD;EACF,CAAC;EAED,MAAMQ,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACFhC,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMa,QAAQ,GAAG,MAAM7B,UAAU,CAAC8C,cAAc,CAAC;QAC/Cd,OAAO,EAAErB,IAAI,CAACoB,EAAE;QAChBkB,gBAAgB,EAAEF,eAAe;QACjCG,YAAY,EAAEF;MAChB,CAAC,CAAC;MAEF,IAAInB,QAAQ,CAACQ,OAAO,EAAE;QACpB,OAAO;UAAEA,OAAO,EAAE,IAAI;UAAEE,OAAO,EAAEV,QAAQ,CAACU;QAAQ,CAAC;MACrD;IACF,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZ,MAAMe,YAAY,GAAGf,GAAG,CAACgB,OAAO,IAAI,wBAAwB;MAC5DvB,QAAQ,CAACsB,YAAY,CAAC;MACtB,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEtB,KAAK,EAAEuB;MAAa,CAAC;IAChD;EACF,CAAC;EAED,MAAMa,KAAK,GAAG;IACZxC,IAAI;IACJE,OAAO;IACPE,KAAK;IACLW,KAAK;IACLc,MAAM;IACNC,MAAM;IACNC,iBAAiB;IACjBI,cAAc;IACdM,eAAe,EAAE,CAAC,CAACzC;EACrB,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CAvIWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}