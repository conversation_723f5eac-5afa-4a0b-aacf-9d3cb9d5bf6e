{"ast": null, "code": "// API service for communicating with the FastAPI backend\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  // Helper method to handle API responses\n  async handleResponse(response) {\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n    return response.json();\n  }\n\n  // Authentication endpoints\n  async login(username, password) {\n    const response = await fetch(`${this.baseURL}/login`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        username,\n        password\n      })\n    });\n    return this.handleResponse(response);\n  }\n  async signup(userData) {\n    const response = await fetch(`${this.baseURL}/signup`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(userData)\n    });\n    return this.handleResponse(response);\n  }\n\n  // Medical query endpoints\n  async submitQuery(queryData) {\n    const response = await fetch(`${this.baseURL}/query`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(queryData)\n    });\n    return this.handleResponse(response);\n  }\n  async submitQueryWithFile(formData) {\n    const response = await fetch(`${this.baseURL}/query-with-file`, {\n      method: 'POST',\n      body: formData // FormData handles its own Content-Type\n    });\n    return this.handleResponse(response);\n  }\n\n  // Query history endpoints\n  async getQueryHistory(userId, limit = 20) {\n    const response = await fetch(`${this.baseURL}/history/${userId}?limit=${limit}`);\n    return this.handleResponse(response);\n  }\n\n  // PubMed research endpoints\n  async searchPubMed(query, numArticles = 5) {\n    const response = await fetch(`${this.baseURL}/pubmed`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        query,\n        num_articles: numArticles\n      })\n    });\n    return this.handleResponse(response);\n  }\n\n  // Profile management endpoints\n  async updateProfile(profileData) {\n    const response = await fetch(`${this.baseURL}/profile/update`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(profileData)\n    });\n    return this.handleResponse(response);\n  }\n  async changePassword(passwordData) {\n    const response = await fetch(`${this.baseURL}/profile/change-password`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(passwordData)\n    });\n    return this.handleResponse(response);\n  }\n\n  // User statistics\n  async getUserStats(userId) {\n    const response = await fetch(`${this.baseURL}/stats/${userId}`);\n    return this.handleResponse(response);\n  }\n\n  // PDF generation\n  async generatePDF(pdfData) {\n    const response = await fetch(`${this.baseURL}/generate-pdf`, {\n      method: 'POST',\n      body: pdfData // FormData\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return response.blob(); // Return blob for PDF download\n  }\n\n  // Health check\n  async healthCheck() {\n    const response = await fetch(`${this.baseURL}/health`);\n    return this.handleResponse(response);\n  }\n\n  // Test AI functionality\n  async testAI(query = \"What is diabetes?\", language = \"English\") {\n    const response = await fetch(`${this.baseURL}/test-ai?query=${encodeURIComponent(query)}&language=${encodeURIComponent(language)}`, {\n      method: 'POST'\n    });\n    return this.handleResponse(response);\n  }\n}\n\n// Create and export a singleton instance\nconst apiService = new ApiService();\nexport default apiService;\n\n// Export individual methods for convenience\nexport const {\n  login,\n  signup,\n  submitQuery,\n  submitQueryWithFile,\n  getQueryHistory,\n  searchPubMed,\n  updateProfile,\n  changePassword,\n  getUserStats,\n  generatePDF,\n  healthCheck,\n  testAI\n} = apiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "handleResponse", "response", "ok", "errorData", "json", "catch", "Error", "detail", "status", "login", "username", "password", "fetch", "method", "headers", "body", "JSON", "stringify", "signup", "userData", "submitQuery", "queryData", "submitQueryWithFile", "formData", "getQueryHistory", "userId", "limit", "searchPubMed", "query", "numArticles", "num_articles", "updateProfile", "profileData", "changePassword", "passwordData", "getUserStats", "generatePDF", "pdfData", "blob", "healthCheck", "testAI", "language", "encodeURIComponent", "apiService"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/services/api.js"], "sourcesContent": ["// API service for communicating with the FastAPI backend\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  // Helper method to handle API responses\n  async handleResponse(response) {\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n    return response.json();\n  }\n\n  // Authentication endpoints\n  async login(username, password) {\n    const response = await fetch(`${this.baseURL}/login`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ username, password }),\n    });\n    return this.handleResponse(response);\n  }\n\n  async signup(userData) {\n    const response = await fetch(`${this.baseURL}/signup`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(userData),\n    });\n    return this.handleResponse(response);\n  }\n\n  // Medical query endpoints\n  async submitQuery(queryData) {\n    const response = await fetch(`${this.baseURL}/query`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(queryData),\n    });\n    return this.handleResponse(response);\n  }\n\n  async submitQueryWithFile(formData) {\n    const response = await fetch(`${this.baseURL}/query-with-file`, {\n      method: 'POST',\n      body: formData, // FormData handles its own Content-Type\n    });\n    return this.handleResponse(response);\n  }\n\n  // Query history endpoints\n  async getQueryHistory(userId, limit = 20) {\n    const response = await fetch(`${this.baseURL}/history/${userId}?limit=${limit}`);\n    return this.handleResponse(response);\n  }\n\n  // PubMed research endpoints\n  async searchPubMed(query, numArticles = 5) {\n    const response = await fetch(`${this.baseURL}/pubmed`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ query, num_articles: numArticles }),\n    });\n    return this.handleResponse(response);\n  }\n\n  // Profile management endpoints\n  async updateProfile(profileData) {\n    const response = await fetch(`${this.baseURL}/profile/update`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(profileData),\n    });\n    return this.handleResponse(response);\n  }\n\n  async changePassword(passwordData) {\n    const response = await fetch(`${this.baseURL}/profile/change-password`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(passwordData),\n    });\n    return this.handleResponse(response);\n  }\n\n  // User statistics\n  async getUserStats(userId) {\n    const response = await fetch(`${this.baseURL}/stats/${userId}`);\n    return this.handleResponse(response);\n  }\n\n  // PDF generation\n  async generatePDF(pdfData) {\n    const response = await fetch(`${this.baseURL}/generate-pdf`, {\n      method: 'POST',\n      body: pdfData, // FormData\n    });\n    \n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    \n    return response.blob(); // Return blob for PDF download\n  }\n\n  // Health check\n  async healthCheck() {\n    const response = await fetch(`${this.baseURL}/health`);\n    return this.handleResponse(response);\n  }\n\n  // Test AI functionality\n  async testAI(query = \"What is diabetes?\", language = \"English\") {\n    const response = await fetch(`${this.baseURL}/test-ai?query=${encodeURIComponent(query)}&language=${encodeURIComponent(language)}`, {\n      method: 'POST',\n    });\n    return this.handleResponse(response);\n  }\n}\n\n// Create and export a singleton instance\nconst apiService = new ApiService();\nexport default apiService;\n\n// Export individual methods for convenience\nexport const {\n  login,\n  signup,\n  submitQuery,\n  submitQueryWithFile,\n  getQueryHistory,\n  searchPubMed,\n  updateProfile,\n  changePassword,\n  getUserStats,\n  generatePDF,\n  healthCheck,\n  testAI\n} = apiService;\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGN,YAAY;EAC7B;;EAEA;EACA,MAAMO,cAAcA,CAACC,QAAQ,EAAE;IAC7B,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACzD,MAAM,IAAIC,KAAK,CAACH,SAAS,CAACI,MAAM,IAAI,uBAAuBN,QAAQ,CAACO,MAAM,EAAE,CAAC;IAC/E;IACA,OAAOP,QAAQ,CAACG,IAAI,CAAC,CAAC;EACxB;;EAEA;EACA,MAAMK,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,MAAMV,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,QAAQ,EAAE;MACpDc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEP,QAAQ;QAAEC;MAAS,CAAC;IAC7C,CAAC,CAAC;IACF,OAAO,IAAI,CAACX,cAAc,CAACC,QAAQ,CAAC;EACtC;EAEA,MAAMiB,MAAMA,CAACC,QAAQ,EAAE;IACrB,MAAMlB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,SAAS,EAAE;MACrDc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;IAC/B,CAAC,CAAC;IACF,OAAO,IAAI,CAACnB,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAMmB,WAAWA,CAACC,SAAS,EAAE;IAC3B,MAAMpB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,QAAQ,EAAE;MACpDc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,SAAS;IAChC,CAAC,CAAC;IACF,OAAO,IAAI,CAACrB,cAAc,CAACC,QAAQ,CAAC;EACtC;EAEA,MAAMqB,mBAAmBA,CAACC,QAAQ,EAAE;IAClC,MAAMtB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,kBAAkB,EAAE;MAC9Dc,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEQ,QAAQ,CAAE;IAClB,CAAC,CAAC;IACF,OAAO,IAAI,CAACvB,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAMuB,eAAeA,CAACC,MAAM,EAAEC,KAAK,GAAG,EAAE,EAAE;IACxC,MAAMzB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,YAAY0B,MAAM,UAAUC,KAAK,EAAE,CAAC;IAChF,OAAO,IAAI,CAAC1B,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAM0B,YAAYA,CAACC,KAAK,EAAEC,WAAW,GAAG,CAAC,EAAE;IACzC,MAAM5B,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,SAAS,EAAE;MACrDc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEW,KAAK;QAAEE,YAAY,EAAED;MAAY,CAAC;IAC3D,CAAC,CAAC;IACF,OAAO,IAAI,CAAC7B,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAM8B,aAAaA,CAACC,WAAW,EAAE;IAC/B,MAAM/B,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,iBAAiB,EAAE;MAC7Dc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACe,WAAW;IAClC,CAAC,CAAC;IACF,OAAO,IAAI,CAAChC,cAAc,CAACC,QAAQ,CAAC;EACtC;EAEA,MAAMgC,cAAcA,CAACC,YAAY,EAAE;IACjC,MAAMjC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,0BAA0B,EAAE;MACtEc,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACiB,YAAY;IACnC,CAAC,CAAC;IACF,OAAO,IAAI,CAAClC,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAMkC,YAAYA,CAACV,MAAM,EAAE;IACzB,MAAMxB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,UAAU0B,MAAM,EAAE,CAAC;IAC/D,OAAO,IAAI,CAACzB,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAMmC,WAAWA,CAACC,OAAO,EAAE;IACzB,MAAMpC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,eAAe,EAAE;MAC3Dc,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEsB,OAAO,CAAE;IACjB,CAAC,CAAC;IAEF,IAAI,CAACpC,QAAQ,CAACC,EAAE,EAAE;MAChB,MAAM,IAAII,KAAK,CAAC,uBAAuBL,QAAQ,CAACO,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAOP,QAAQ,CAACqC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1B;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAG;IAClB,MAAMtC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,SAAS,CAAC;IACtD,OAAO,IAAI,CAACC,cAAc,CAACC,QAAQ,CAAC;EACtC;;EAEA;EACA,MAAMuC,MAAMA,CAACZ,KAAK,GAAG,mBAAmB,EAAEa,QAAQ,GAAG,SAAS,EAAE;IAC9D,MAAMxC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAG,IAAI,CAACb,OAAO,kBAAkB2C,kBAAkB,CAACd,KAAK,CAAC,aAAac,kBAAkB,CAACD,QAAQ,CAAC,EAAE,EAAE;MAClI5B,MAAM,EAAE;IACV,CAAC,CAAC;IACF,OAAO,IAAI,CAACb,cAAc,CAACC,QAAQ,CAAC;EACtC;AACF;;AAEA;AACA,MAAM0C,UAAU,GAAG,IAAI9C,UAAU,CAAC,CAAC;AACnC,eAAe8C,UAAU;;AAEzB;AACA,OAAO,MAAM;EACXlC,KAAK;EACLS,MAAM;EACNE,WAAW;EACXE,mBAAmB;EACnBE,eAAe;EACfG,YAAY;EACZI,aAAa;EACbE,cAAc;EACdE,YAAY;EACZC,WAAW;EACXG,WAAW;EACXC;AACF,CAAC,GAAGG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}