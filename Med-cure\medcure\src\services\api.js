// API service for communicating with the FastAPI backend
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Authentication endpoints
  async login(username, password) {
    const response = await fetch(`${this.baseURL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });
    return this.handleResponse(response);
  }

  async signup(userData) {
    const response = await fetch(`${this.baseURL}/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });
    return this.handleResponse(response);
  }

  // Medical query endpoints
  async submitQuery(queryData) {
    const response = await fetch(`${this.baseURL}/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(queryData),
    });
    return this.handleResponse(response);
  }

  async submitQueryWithFile(formData) {
    const response = await fetch(`${this.baseURL}/query-with-file`, {
      method: 'POST',
      body: formData, // FormData handles its own Content-Type
    });
    return this.handleResponse(response);
  }

  // Query history endpoints
  async getQueryHistory(userId, limit = 20) {
    const response = await fetch(`${this.baseURL}/history/${userId}?limit=${limit}`);
    return this.handleResponse(response);
  }

  // PubMed research endpoints
  async searchPubMed(query, numArticles = 5) {
    const response = await fetch(`${this.baseURL}/pubmed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, num_articles: numArticles }),
    });
    return this.handleResponse(response);
  }

  // Profile management endpoints
  async updateProfile(profileData) {
    const response = await fetch(`${this.baseURL}/profile/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(profileData),
    });
    return this.handleResponse(response);
  }

  async changePassword(passwordData) {
    const response = await fetch(`${this.baseURL}/profile/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(passwordData),
    });
    return this.handleResponse(response);
  }

  // User statistics
  async getUserStats(userId) {
    const response = await fetch(`${this.baseURL}/stats/${userId}`);
    return this.handleResponse(response);
  }

  // PDF generation
  async generatePDF(pdfData) {
    const response = await fetch(`${this.baseURL}/generate-pdf`, {
      method: 'POST',
      body: pdfData, // FormData
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.blob(); // Return blob for PDF download
  }

  // Health check
  async healthCheck() {
    const response = await fetch(`${this.baseURL}/health`);
    return this.handleResponse(response);
  }

  // Test AI functionality
  async testAI(query = "What is diabetes?", language = "English") {
    const response = await fetch(`${this.baseURL}/test-ai?query=${encodeURIComponent(query)}&language=${encodeURIComponent(language)}`, {
      method: 'POST',
    });
    return this.handleResponse(response);
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;

// Export individual methods for convenience
export const {
  login,
  signup,
  submitQuery,
  submitQueryWithFile,
  getQueryHistory,
  searchPubMed,
  updateProfile,
  changePassword,
  getUserStats,
  generatePDF,
  healthCheck,
  testAI
} = apiService;
