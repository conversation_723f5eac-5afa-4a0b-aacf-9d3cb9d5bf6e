{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\RoleSelector.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './RoleSelector.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst roles = [{\n  name: 'Student',\n  emoji: '🎓',\n  description: 'Explore medical knowledge through research and case studies'\n}, {\n  name: 'Doctor',\n  emoji: '👨‍⚕️',\n  description: 'Analyze patient data and treatment methodologies'\n}, {\n  name: 'Common User',\n  emoji: '🧑‍💼',\n  description: 'Understand medical conditions in simple terms'\n}];\nconst RoleSelector = () => {\n  _s();\n  const [currentRole, setCurrentRole] = useState('Doctor');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"role-selector-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Select Your Role for this Query\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cards-container\",\n      children: roles.map(role => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `card ${currentRole === role.name ? 'active' : ''}`,\n        onClick: () => setCurrentRole(role.name),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"emoji\",\n          children: role.emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: role.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: role.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"select-btn\",\n          children: [\"SELECT \", role.name.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, role.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"current-role\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Current Role:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), \" \", roles.find(r => r.name === currentRole).emoji, \" \", currentRole]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleSelector, \"0RyMTgrFWE5iVDYvZR6xFTu8ZRU=\");\n_c = RoleSelector;\nexport default RoleSelector;\nvar _c;\n$RefreshReg$(_c, \"RoleSelector\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "roles", "name", "emoji", "description", "RoleSelector", "_s", "currentRole", "setCurrentRole", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "role", "onClick", "toUpperCase", "find", "r", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/RoleSelector.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './RoleSelector.css';\r\n\r\nconst roles = [\r\n  {\r\n    name: 'Student',\r\n    emoji: '🎓',\r\n    description: 'Explore medical knowledge through research and case studies',\r\n  },\r\n  {\r\n    name: 'Doctor',\r\n    emoji: '👨‍⚕️',\r\n    description: 'Analyze patient data and treatment methodologies',\r\n  },\r\n  {\r\n    name: 'Common User',\r\n    emoji: '🧑‍💼',\r\n    description: 'Understand medical conditions in simple terms',\r\n  },\r\n];\r\n\r\nconst RoleSelector = () => {\r\n  const [currentRole, setCurrentRole] = useState('Doctor');\r\n\r\n  return (\r\n    <div className=\"role-selector-wrapper\">\r\n      <h2>Select Your Role for this Query</h2>\r\n      <div className=\"cards-container\">\r\n        {roles.map((role) => (\r\n          <div\r\n            key={role.name}\r\n            className={`card ${currentRole === role.name ? 'active' : ''}`}\r\n            onClick={() => setCurrentRole(role.name)}\r\n          >\r\n            <div className=\"emoji\">{role.emoji}</div>\r\n            <h3>{role.name}</h3>\r\n            <p>{role.description}</p>\r\n            <button className=\"select-btn\">SELECT {role.name.toUpperCase()}</button>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div className=\"current-role\">\r\n        <strong>Current Role:</strong> {roles.find(r => r.name === currentRole).emoji} {currentRole}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RoleSelector;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,KAAK,GAAG,CACZ;EACEC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,QAAQ,CAAC;EAExD,oBACEE,OAAA;IAAKS,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCV,OAAA;MAAAU,QAAA,EAAI;IAA+B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxCd,OAAA;MAAKS,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BT,KAAK,CAACc,GAAG,CAAEC,IAAI,iBACdhB,OAAA;QAEES,SAAS,EAAE,QAAQF,WAAW,KAAKS,IAAI,CAACd,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC/De,OAAO,EAAEA,CAAA,KAAMT,cAAc,CAACQ,IAAI,CAACd,IAAI,CAAE;QAAAQ,QAAA,gBAEzCV,OAAA;UAAKS,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEM,IAAI,CAACb;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCd,OAAA;UAAAU,QAAA,EAAKM,IAAI,CAACd;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpBd,OAAA;UAAAU,QAAA,EAAIM,IAAI,CAACZ;QAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBd,OAAA;UAAQS,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,SAAO,EAACM,IAAI,CAACd,IAAI,CAACgB,WAAW,CAAC,CAAC;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA,GAPnEE,IAAI,CAACd,IAAI;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQX,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNd,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BV,OAAA;QAAAU,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACb,KAAK,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,IAAI,KAAKK,WAAW,CAAC,CAACJ,KAAK,EAAC,GAAC,EAACI,WAAW;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACR,EAAA,CAzBID,YAAY;AAAAgB,EAAA,GAAZhB,YAAY;AA2BlB,eAAeA,YAAY;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}