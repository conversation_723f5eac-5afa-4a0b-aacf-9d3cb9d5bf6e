import React, { useState, useEffect } from 'react';
import { useAuth } from './contexts/AuthContext';
import apiService from './services/api';
import AuthModal from './components/AuthModal';
import './Settings.css';
import Footer from './Footer';

const Settings = () => {
  const { user, isAuthenticated, updateUserProfile, changePassword, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [userStats, setUserStats] = useState(null);

  // Profile form states
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');

  // Password form states
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Initialize form data when user is available
  useEffect(() => {
    if (user) {
      setFullName(user.name || '');
      setEmail(user.email || '');
      fetchUserStats();
    }
  }, [user]);

  const fetchUserStats = async () => {
    if (!user) return;

    try {
      const stats = await apiService.getUserStats(user.id);
      setUserStats(stats);
    } catch (err) {
      console.error('Error fetching user stats:', err);
    }
  };

  const handleUpdateProfile = async () => {
    if (!fullName.trim()) {
      setError('Name is required');
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      const result = await updateUserProfile({
        name: fullName.trim(),
        email: email.trim()
      });

      if (result.success) {
        setMessage('Profile updated successfully!');
      } else {
        setError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      setError('An error occurred while updating profile');
    } finally {
      setLoading(false);
    }
  };

  const togglePassword = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError("All password fields are required.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("New passwords do not match.");
      return;
    }

    if (newPassword.length < 6) {
      setError("New password must be at least 6 characters long.");
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      const result = await changePassword(currentPassword, newPassword);

      if (result.success) {
        setMessage('Password changed successfully!');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        setError(result.error || 'Failed to change password');
      }
    } catch (err) {
      setError('An error occurred while changing password');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    setMessage('You have been logged out successfully.');
  };

  if (!isAuthenticated) {
    return (
      <div className="settings-container">
        <div className="auth-required-message">
          <h2>Authentication Required</h2>
          <p>Please sign in to access your settings.</p>
          <button
            className="auth-btn"
            onClick={() => setShowAuthModal(true)}
          >
            Sign In
          </button>
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h1>Settings</h1>
        <p>Manage your account and preferences</p>
      </div>

      {message && <div className="success-message">{message}</div>}
      {error && <div className="error-message">{error}</div>}

      <div className="tabs">
        <button
          className={activeTab === 'profile' ? 'tab active' : 'tab'}
          onClick={() => {
            setActiveTab('profile');
            setMessage('');
            setError('');
          }}
        >
          Profile
        </button>
        <button
          className={activeTab === 'preferences' ? 'tab active' : 'tab'}
          onClick={() => {
            setActiveTab('preferences');
            setMessage('');
            setError('');
          }}
        >
          Account
        </button>
      </div>

      {activeTab === 'profile' ? (
        <div className="tab-content">
          <h2>Profile Information</h2>
          <div className="profile-card">
            <div className="profile-avatar">
              <div className="avatar-circle">
                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
              </div>
            </div>
            <div className="profile-details">
              <p><strong>Username:</strong> {user.username}</p>
              <p><strong>Name:</strong> {user.name}</p>
              <p><strong>Role:</strong> {user.role}</p>
              {userStats && (
                <div className="user-stats">
                  <p><strong>Total Queries:</strong> {userStats.total_queries || 0}</p>
                  <p><strong>Member Since:</strong> {new Date(userStats.created_at).toLocaleDateString()}</p>
                </div>
              )}
            </div>
          </div>

          <h2>Update Profile</h2>
          <div className="form">
            <label>Full Name</label>
            <input
              type="text"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              disabled={loading}
              placeholder="Enter your full name"
            />
            <label>Email Address</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
              placeholder="Enter your email address"
            />
            <button
              className="update-button"
              onClick={handleUpdateProfile}
              disabled={loading}
            >
              {loading ? 'UPDATING...' : 'UPDATE PROFILE'}
            </button>
          </div>

          <h2>Security</h2>
          <div className="form">
            <label>Current Password</label>
            <div className="password-input">
              <input
                type={showPasswords.current ? 'text' : 'password'}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                disabled={loading}
                placeholder="Enter current password"
              />
              <span onClick={() => togglePassword('current')} className="eye-icon">👁️</span>
            </div>

            <label>New Password</label>
            <div className="password-input">
              <input
                type={showPasswords.new ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                disabled={loading}
                placeholder="Enter new password (min 6 characters)"
              />
              <span onClick={() => togglePassword('new')} className="eye-icon">👁️</span>
            </div>

            <label>Confirm New Password</label>
            <div className="password-input">
              <input
                type={showPasswords.confirm ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={loading}
                placeholder="Confirm new password"
              />
              <span onClick={() => togglePassword('confirm')} className="eye-icon">👁️</span>
            </div>

            <button
              className="update-button"
              onClick={handleChangePassword}
              disabled={loading}
            >
              {loading ? 'CHANGING...' : 'CHANGE PASSWORD'}
            </button>
          </div>
        </div>
      ) : (
        <div className="tab-content">
          <h2>Account Management</h2>

          <div className="account-section">
            <h3>Account Information</h3>
            <div className="info-card">
              <p><strong>Account Status:</strong> <span className="status-active">Active</span></p>
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Role:</strong> {user.role}</p>
            </div>
          </div>

          <div className="account-section">
            <h3>Data & Privacy</h3>
            <p>Your query history and personal data are securely stored and encrypted.</p>
            <div className="privacy-options">
              <label>
                <input type="checkbox" defaultChecked disabled />
                Store query history for improved experience
              </label>
              <label>
                <input type="checkbox" defaultChecked disabled />
                Allow anonymous usage analytics
              </label>
            </div>
          </div>

          <div className="account-section danger-zone">
            <h3>Danger Zone</h3>
            <p>These actions cannot be undone. Please be careful.</p>
            <button
              className="logout-button"
              onClick={handleLogout}
            >
              🚪 Sign Out
            </button>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default Settings;
