{"ast": null, "code": "export { default } from \"./Avatar.js\";\nexport { default as avatarClasses } from \"./avatarClasses.js\";\nexport * from \"./avatarClasses.js\";", "map": {"version": 3, "names": ["default", "avatarClasses"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/material/esm/Avatar/index.js"], "sourcesContent": ["export { default } from \"./Avatar.js\";\nexport { default as avatarClasses } from \"./avatarClasses.js\";\nexport * from \"./avatarClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,aAAa,QAAQ,oBAAoB;AAC7D,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}