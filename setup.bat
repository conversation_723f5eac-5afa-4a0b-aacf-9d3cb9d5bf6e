@echo off
echo 🚀 Setting up Med-cure application...
echo ======================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.8+ and try again.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 16+ and try again.
    pause
    exit /b 1
)

REM Backend setup
echo 🐍 Setting up backend...
cd Backend

REM Create virtual environment
echo Creating Python virtual environment...
python -m venv venv

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install Python dependencies
echo Installing Python dependencies...
pip install -r requirements.txt

REM Copy environment file
if not exist .env (
    copy .env.example .env
    echo ✅ Created Backend\.env file
) else (
    echo ⚠️  Backend\.env already exists
)

echo ✅ Backend setup complete!

REM Frontend setup
echo.
echo ⚛️  Setting up frontend...
cd ..\Med-cure\medcure

REM Install Node.js dependencies
echo Installing Node.js dependencies...
npm install

REM Copy environment file
if not exist .env (
    copy .env.example .env
    echo ✅ Created frontend .env file
) else (
    echo ⚠️  Frontend .env already exists
)

echo ✅ Frontend setup complete!

REM Final instructions
echo.
echo 🎉 Setup finished successfully!
echo ==============================
echo.
echo 📝 Next steps:
echo 1. Edit Backend\.env with your configuration:
echo    - Set your Google AI API key
echo    - Configure your PostgreSQL database URL
echo.
echo 2. Edit Med-cure\medcure\.env if needed:
echo    - Default backend URL is http://localhost:8000
echo.
echo 3. Start the application:
echo    Backend:  cd Backend ^&^& python app.py
echo    Frontend: cd Med-cure\medcure ^&^& npm start
echo.
echo 🌐 The application will be available at:
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:8000
echo    API Docs: http://localhost:8000/docs
echo.
echo 📚 See INTEGRATION_GUIDE.md for detailed instructions
echo.
pause
