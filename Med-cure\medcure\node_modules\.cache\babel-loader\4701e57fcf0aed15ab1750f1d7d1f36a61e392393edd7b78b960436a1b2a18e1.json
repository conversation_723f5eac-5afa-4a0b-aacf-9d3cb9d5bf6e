{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\Settings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from './contexts/AuthContext';\nimport apiService from './services/api';\nimport AuthModal from './components/AuthModal';\nimport './Settings.css';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    updateUserProfile,\n    changePassword,\n    logout\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [userStats, setUserStats] = useState(null);\n\n  // Profile form states\n  const [fullName, setFullName] = useState('');\n  const [email, setEmail] = useState('');\n\n  // Password form states\n  const [currentPassword, setCurrentPassword] = useState('');\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n\n  // Initialize form data when user is available\n  useEffect(() => {\n    if (user) {\n      setFullName(user.name || '');\n      setEmail(user.email || '');\n      fetchUserStats();\n    }\n  }, [user]);\n  const fetchUserStats = async () => {\n    if (!user) return;\n    try {\n      const stats = await apiService.getUserStats(user.id);\n      setUserStats(stats);\n    } catch (err) {\n      console.error('Error fetching user stats:', err);\n    }\n  };\n  const handleUpdateProfile = async () => {\n    if (!fullName.trim()) {\n      setError('Name is required');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setMessage('');\n    try {\n      const result = await updateUserProfile({\n        name: fullName.trim(),\n        email: email.trim()\n      });\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n      } else {\n        setError(result.error || 'Failed to update profile');\n      }\n    } catch (err) {\n      setError('An error occurred while updating profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const togglePassword = field => {\n    setShowPasswords(prev => ({\n      ...prev,\n      [field]: !prev[field]\n    }));\n  };\n  const handleChangePassword = async () => {\n    if (!currentPassword || !newPassword || !confirmPassword) {\n      setError(\"All password fields are required.\");\n      return;\n    }\n    if (newPassword !== confirmPassword) {\n      setError(\"New passwords do not match.\");\n      return;\n    }\n    if (newPassword.length < 6) {\n      setError(\"New password must be at least 6 characters long.\");\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setMessage('');\n    try {\n      const result = await changePassword(currentPassword, newPassword);\n      if (result.success) {\n        setMessage('Password changed successfully!');\n        setCurrentPassword('');\n        setNewPassword('');\n        setConfirmPassword('');\n      } else {\n        setError(result.error || 'Failed to change password');\n      }\n    } catch (err) {\n      setError('An error occurred while changing password');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = () => {\n    logout();\n    setMessage('You have been logged out successfully.');\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please sign in to access your settings.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"auth-btn\",\n          onClick: () => setShowAuthModal(true),\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AuthModal, {\n        isOpen: showAuthModal,\n        onClose: () => setShowAuthModal(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"settings-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage your account and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'profile' ? 'tab active' : 'tab',\n        onClick: () => {\n          setActiveTab('profile');\n          setMessage('');\n          setError('');\n        },\n        children: \"Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'preferences' ? 'tab active' : 'tab',\n        onClick: () => {\n          setActiveTab('preferences');\n          setMessage('');\n          setError('');\n        },\n        children: \"Account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), activeTab === 'profile' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Profile Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"avatar-circle\",\n            children: user.name ? user.name.charAt(0).toUpperCase() : 'U'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Username:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 18\n            }, this), \" \", user.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 18\n            }, this), \" \", user.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 18\n            }, this), \" \", user.role]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), userStats && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Queries:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 22\n              }, this), \" \", userStats.total_queries || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Member Since:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 22\n              }, this), \" \", new Date(userStats.created_at).toLocaleDateString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Update Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Full Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: fullName,\n          onChange: e => setFullName(e.target.value),\n          disabled: loading,\n          placeholder: \"Enter your full name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          disabled: loading,\n          placeholder: \"Enter your email address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"update-button\",\n          onClick: handleUpdateProfile,\n          disabled: loading,\n          children: loading ? 'UPDATING...' : 'UPDATE PROFILE'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Security\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Current Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"password-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: showPasswords.current ? 'text' : 'password',\n            value: currentPassword,\n            onChange: e => setCurrentPassword(e.target.value),\n            disabled: loading,\n            placeholder: \"Enter current password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => togglePassword('current'),\n            className: \"eye-icon\",\n            children: \"\\uD83D\\uDC41\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"New Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"password-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: showPasswords.new ? 'text' : 'password',\n            value: newPassword,\n            onChange: e => setNewPassword(e.target.value),\n            disabled: loading,\n            placeholder: \"Enter new password (min 6 characters)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => togglePassword('new'),\n            className: \"eye-icon\",\n            children: \"\\uD83D\\uDC41\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Confirm New Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"password-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: showPasswords.confirm ? 'text' : 'password',\n            value: confirmPassword,\n            onChange: e => setConfirmPassword(e.target.value),\n            disabled: loading,\n            placeholder: \"Confirm new password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => togglePassword('confirm'),\n            className: \"eye-icon\",\n            children: \"\\uD83D\\uDC41\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"update-button\",\n          onClick: handleChangePassword,\n          disabled: loading,\n          children: loading ? 'CHANGING...' : 'CHANGE PASSWORD'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Account Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Account Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Account Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 18\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 51\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"User ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 18\n            }, this), \" \", user.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 18\n            }, this), \" \", user.role]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Data & Privacy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your query history and personal data are securely stored and encrypted.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"privacy-options\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              defaultChecked: true,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), \"Store query history for improved experience\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              defaultChecked: true,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), \"Allow anonymous usage analytics\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-section danger-zone\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Danger Zone\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"These actions cannot be undone. Please be careful.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-button\",\n          onClick: handleLogout,\n          children: \"\\uD83D\\uDEAA Sign Out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"GfSCJc91BSyilJmWN5FW0z0Yq/w=\", false, function () {\n  return [useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "apiService", "AuthModal", "Footer", "jsxDEV", "_jsxDEV", "Settings", "_s", "user", "isAuthenticated", "updateUserProfile", "changePassword", "logout", "activeTab", "setActiveTab", "showAuthModal", "setShowAuthModal", "loading", "setLoading", "message", "setMessage", "error", "setError", "userStats", "setUserStats", "fullName", "setFullName", "email", "setEmail", "currentPassword", "setCurrentPassword", "newPassword", "setNewPassword", "confirmPassword", "setConfirmPassword", "showPasswords", "setShowPasswords", "current", "new", "confirm", "name", "fetchUserStats", "stats", "getUserStats", "id", "err", "console", "handleUpdateProfile", "trim", "result", "success", "togglePassword", "field", "prev", "handleChangePassword", "length", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isOpen", "onClose", "char<PERSON>t", "toUpperCase", "username", "role", "total_queries", "Date", "created_at", "toLocaleDateString", "type", "value", "onChange", "e", "target", "disabled", "placeholder", "defaultChecked", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/Settings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useAuth } from './contexts/AuthContext';\r\nimport apiService from './services/api';\r\nimport AuthModal from './components/AuthModal';\r\nimport './Settings.css';\r\nimport Footer from './Footer';\r\n\r\nconst Settings = () => {\r\n  const { user, isAuthenticated, updateUserProfile, changePassword, logout } = useAuth();\r\n  const [activeTab, setActiveTab] = useState('profile');\r\n  const [showAuthModal, setShowAuthModal] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [userStats, setUserStats] = useState(null);\r\n\r\n  // Profile form states\r\n  const [fullName, setFullName] = useState('');\r\n  const [email, setEmail] = useState('');\r\n\r\n  // Password form states\r\n  const [currentPassword, setCurrentPassword] = useState('');\r\n  const [newPassword, setNewPassword] = useState('');\r\n  const [confirmPassword, setConfirmPassword] = useState('');\r\n  const [showPasswords, setShowPasswords] = useState({\r\n    current: false,\r\n    new: false,\r\n    confirm: false,\r\n  });\r\n\r\n  // Initialize form data when user is available\r\n  useEffect(() => {\r\n    if (user) {\r\n      setFullName(user.name || '');\r\n      setEmail(user.email || '');\r\n      fetchUserStats();\r\n    }\r\n  }, [user]);\r\n\r\n  const fetchUserStats = async () => {\r\n    if (!user) return;\r\n\r\n    try {\r\n      const stats = await apiService.getUserStats(user.id);\r\n      setUserStats(stats);\r\n    } catch (err) {\r\n      console.error('Error fetching user stats:', err);\r\n    }\r\n  };\r\n\r\n  const handleUpdateProfile = async () => {\r\n    if (!fullName.trim()) {\r\n      setError('Name is required');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setMessage('');\r\n\r\n    try {\r\n      const result = await updateUserProfile({\r\n        name: fullName.trim(),\r\n        email: email.trim()\r\n      });\r\n\r\n      if (result.success) {\r\n        setMessage('Profile updated successfully!');\r\n      } else {\r\n        setError(result.error || 'Failed to update profile');\r\n      }\r\n    } catch (err) {\r\n      setError('An error occurred while updating profile');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const togglePassword = (field) => {\r\n    setShowPasswords(prev => ({\r\n      ...prev,\r\n      [field]: !prev[field],\r\n    }));\r\n  };\r\n\r\n  const handleChangePassword = async () => {\r\n    if (!currentPassword || !newPassword || !confirmPassword) {\r\n      setError(\"All password fields are required.\");\r\n      return;\r\n    }\r\n\r\n    if (newPassword !== confirmPassword) {\r\n      setError(\"New passwords do not match.\");\r\n      return;\r\n    }\r\n\r\n    if (newPassword.length < 6) {\r\n      setError(\"New password must be at least 6 characters long.\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setMessage('');\r\n\r\n    try {\r\n      const result = await changePassword(currentPassword, newPassword);\r\n\r\n      if (result.success) {\r\n        setMessage('Password changed successfully!');\r\n        setCurrentPassword('');\r\n        setNewPassword('');\r\n        setConfirmPassword('');\r\n      } else {\r\n        setError(result.error || 'Failed to change password');\r\n      }\r\n    } catch (err) {\r\n      setError('An error occurred while changing password');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n    setMessage('You have been logged out successfully.');\r\n  };\r\n\r\n  if (!isAuthenticated) {\r\n    return (\r\n      <div className=\"settings-container\">\r\n        <div className=\"auth-required-message\">\r\n          <h2>Authentication Required</h2>\r\n          <p>Please sign in to access your settings.</p>\r\n          <button\r\n            className=\"auth-btn\"\r\n            onClick={() => setShowAuthModal(true)}\r\n          >\r\n            Sign In\r\n          </button>\r\n        </div>\r\n        <AuthModal\r\n          isOpen={showAuthModal}\r\n          onClose={() => setShowAuthModal(false)}\r\n        />\r\n        <Footer />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"settings-container\">\r\n      <div className=\"settings-header\">\r\n        <h1>Settings</h1>\r\n        <p>Manage your account and preferences</p>\r\n      </div>\r\n\r\n      {message && <div className=\"success-message\">{message}</div>}\r\n      {error && <div className=\"error-message\">{error}</div>}\r\n\r\n      <div className=\"tabs\">\r\n        <button\r\n          className={activeTab === 'profile' ? 'tab active' : 'tab'}\r\n          onClick={() => {\r\n            setActiveTab('profile');\r\n            setMessage('');\r\n            setError('');\r\n          }}\r\n        >\r\n          Profile\r\n        </button>\r\n        <button\r\n          className={activeTab === 'preferences' ? 'tab active' : 'tab'}\r\n          onClick={() => {\r\n            setActiveTab('preferences');\r\n            setMessage('');\r\n            setError('');\r\n          }}\r\n        >\r\n          Account\r\n        </button>\r\n      </div>\r\n\r\n      {activeTab === 'profile' ? (\r\n        <div className=\"tab-content\">\r\n          <h2>Profile Information</h2>\r\n          <div className=\"profile-card\">\r\n            <div className=\"profile-avatar\">\r\n              <div className=\"avatar-circle\">\r\n                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}\r\n              </div>\r\n            </div>\r\n            <div className=\"profile-details\">\r\n              <p><strong>Username:</strong> {user.username}</p>\r\n              <p><strong>Name:</strong> {user.name}</p>\r\n              <p><strong>Role:</strong> {user.role}</p>\r\n              {userStats && (\r\n                <div className=\"user-stats\">\r\n                  <p><strong>Total Queries:</strong> {userStats.total_queries || 0}</p>\r\n                  <p><strong>Member Since:</strong> {new Date(userStats.created_at).toLocaleDateString()}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <h2>Update Profile</h2>\r\n          <div className=\"form\">\r\n            <label>Full Name</label>\r\n            <input\r\n              type=\"text\"\r\n              value={fullName}\r\n              onChange={(e) => setFullName(e.target.value)}\r\n              disabled={loading}\r\n              placeholder=\"Enter your full name\"\r\n            />\r\n            <label>Email Address</label>\r\n            <input\r\n              type=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              disabled={loading}\r\n              placeholder=\"Enter your email address\"\r\n            />\r\n            <button\r\n              className=\"update-button\"\r\n              onClick={handleUpdateProfile}\r\n              disabled={loading}\r\n            >\r\n              {loading ? 'UPDATING...' : 'UPDATE PROFILE'}\r\n            </button>\r\n          </div>\r\n\r\n          <h2>Security</h2>\r\n          <div className=\"form\">\r\n            <label>Current Password</label>\r\n            <div className=\"password-input\">\r\n              <input\r\n                type={showPasswords.current ? 'text' : 'password'}\r\n                value={currentPassword}\r\n                onChange={(e) => setCurrentPassword(e.target.value)}\r\n                disabled={loading}\r\n                placeholder=\"Enter current password\"\r\n              />\r\n              <span onClick={() => togglePassword('current')} className=\"eye-icon\">👁️</span>\r\n            </div>\r\n\r\n            <label>New Password</label>\r\n            <div className=\"password-input\">\r\n              <input\r\n                type={showPasswords.new ? 'text' : 'password'}\r\n                value={newPassword}\r\n                onChange={(e) => setNewPassword(e.target.value)}\r\n                disabled={loading}\r\n                placeholder=\"Enter new password (min 6 characters)\"\r\n              />\r\n              <span onClick={() => togglePassword('new')} className=\"eye-icon\">👁️</span>\r\n            </div>\r\n\r\n            <label>Confirm New Password</label>\r\n            <div className=\"password-input\">\r\n              <input\r\n                type={showPasswords.confirm ? 'text' : 'password'}\r\n                value={confirmPassword}\r\n                onChange={(e) => setConfirmPassword(e.target.value)}\r\n                disabled={loading}\r\n                placeholder=\"Confirm new password\"\r\n              />\r\n              <span onClick={() => togglePassword('confirm')} className=\"eye-icon\">👁️</span>\r\n            </div>\r\n\r\n            <button\r\n              className=\"update-button\"\r\n              onClick={handleChangePassword}\r\n              disabled={loading}\r\n            >\r\n              {loading ? 'CHANGING...' : 'CHANGE PASSWORD'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"tab-content\">\r\n          <h2>Account Management</h2>\r\n\r\n          <div className=\"account-section\">\r\n            <h3>Account Information</h3>\r\n            <div className=\"info-card\">\r\n              <p><strong>Account Status:</strong> <span className=\"status-active\">Active</span></p>\r\n              <p><strong>User ID:</strong> {user.id}</p>\r\n              <p><strong>Role:</strong> {user.role}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"account-section\">\r\n            <h3>Data & Privacy</h3>\r\n            <p>Your query history and personal data are securely stored and encrypted.</p>\r\n            <div className=\"privacy-options\">\r\n              <label>\r\n                <input type=\"checkbox\" defaultChecked disabled />\r\n                Store query history for improved experience\r\n              </label>\r\n              <label>\r\n                <input type=\"checkbox\" defaultChecked disabled />\r\n                Allow anonymous usage analytics\r\n              </label>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"account-section danger-zone\">\r\n            <h3>Danger Zone</h3>\r\n            <p>These actions cannot be undone. Please be careful.</p>\r\n            <button\r\n              className=\"logout-button\"\r\n              onClick={handleLogout}\r\n            >\r\n              🚪 Sign Out\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Settings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,iBAAiB;IAAEC,cAAc;IAAEC;EAAO,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACtF,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC;IACjDuC,OAAO,EAAE,KAAK;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIS,IAAI,EAAE;MACRkB,WAAW,CAAClB,IAAI,CAACgC,IAAI,IAAI,EAAE,CAAC;MAC5BZ,QAAQ,CAACpB,IAAI,CAACmB,KAAK,IAAI,EAAE,CAAC;MAC1Bc,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACjC,IAAI,CAAC,CAAC;EAEV,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACjC,IAAI,EAAE;IAEX,IAAI;MACF,MAAMkC,KAAK,GAAG,MAAMzC,UAAU,CAAC0C,YAAY,CAACnC,IAAI,CAACoC,EAAE,CAAC;MACpDpB,YAAY,CAACkB,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEwB,GAAG,CAAC;IAClD;EACF,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,EAAE;MACpB1B,QAAQ,CAAC,kBAAkB,CAAC;MAC5B;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM6B,MAAM,GAAG,MAAMvC,iBAAiB,CAAC;QACrC8B,IAAI,EAAEf,QAAQ,CAACuB,IAAI,CAAC,CAAC;QACrBrB,KAAK,EAAEA,KAAK,CAACqB,IAAI,CAAC;MACpB,CAAC,CAAC;MAEF,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClB9B,UAAU,CAAC,+BAA+B,CAAC;MAC7C,CAAC,MAAM;QACLE,QAAQ,CAAC2B,MAAM,CAAC5B,KAAK,IAAI,0BAA0B,CAAC;MACtD;IACF,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZvB,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,cAAc,GAAIC,KAAK,IAAK;IAChChB,gBAAgB,CAACiB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAG,CAACC,IAAI,CAACD,KAAK;IACtB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzB,eAAe,IAAI,CAACE,WAAW,IAAI,CAACE,eAAe,EAAE;MACxDX,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEA,IAAIS,WAAW,KAAKE,eAAe,EAAE;MACnCX,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEA,IAAIS,WAAW,CAACwB,MAAM,GAAG,CAAC,EAAE;MAC1BjC,QAAQ,CAAC,kDAAkD,CAAC;MAC5D;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM6B,MAAM,GAAG,MAAMtC,cAAc,CAACkB,eAAe,EAAEE,WAAW,CAAC;MAEjE,IAAIkB,MAAM,CAACC,OAAO,EAAE;QAClB9B,UAAU,CAAC,gCAAgC,CAAC;QAC5CU,kBAAkB,CAAC,EAAE,CAAC;QACtBE,cAAc,CAAC,EAAE,CAAC;QAClBE,kBAAkB,CAAC,EAAE,CAAC;MACxB,CAAC,MAAM;QACLZ,QAAQ,CAAC2B,MAAM,CAAC5B,KAAK,IAAI,2BAA2B,CAAC;MACvD;IACF,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZvB,QAAQ,CAAC,2CAA2C,CAAC;IACvD,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAGA,CAAA,KAAM;IACzB5C,MAAM,CAAC,CAAC;IACRQ,UAAU,CAAC,wCAAwC,CAAC;EACtD,CAAC;EAED,IAAI,CAACX,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKoD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCrD,OAAA;QAAKoD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCrD,OAAA;UAAAqD,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCzD,OAAA;UAAAqD,QAAA,EAAG;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9CzD,OAAA;UACEoD,SAAS,EAAC,UAAU;UACpBM,OAAO,EAAEA,CAAA,KAAM/C,gBAAgB,CAAC,IAAI,CAAE;UAAA0C,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNzD,OAAA,CAACH,SAAS;QACR8D,MAAM,EAAEjD,aAAc;QACtBkD,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC,KAAK;MAAE;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACFzD,OAAA,CAACF,MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCrD,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BrD,OAAA;QAAAqD,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBzD,OAAA;QAAAqD,QAAA,EAAG;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,EAEL3C,OAAO,iBAAId,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEvC;IAAO;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC3DzC,KAAK,iBAAIhB,OAAA;MAAKoD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAErC;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEtDzD,OAAA;MAAKoD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBrD,OAAA;QACEoD,SAAS,EAAE5C,SAAS,KAAK,SAAS,GAAG,YAAY,GAAG,KAAM;QAC1DkD,OAAO,EAAEA,CAAA,KAAM;UACbjD,YAAY,CAAC,SAAS,CAAC;UACvBM,UAAU,CAAC,EAAE,CAAC;UACdE,QAAQ,CAAC,EAAE,CAAC;QACd,CAAE;QAAAoC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACEoD,SAAS,EAAE5C,SAAS,KAAK,aAAa,GAAG,YAAY,GAAG,KAAM;QAC9DkD,OAAO,EAAEA,CAAA,KAAM;UACbjD,YAAY,CAAC,aAAa,CAAC;UAC3BM,UAAU,CAAC,EAAE,CAAC;UACdE,QAAQ,CAAC,EAAE,CAAC;QACd,CAAE;QAAAoC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELjD,SAAS,KAAK,SAAS,gBACtBR,OAAA;MAAKoD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrD,OAAA;QAAAqD,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BzD,OAAA;QAAKoD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BlD,IAAI,CAACgC,IAAI,GAAGhC,IAAI,CAACgC,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtD,IAAI,CAAC4D,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDzD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtD,IAAI,CAACgC,IAAI;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCzD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtD,IAAI,CAAC6D,IAAI;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxCvC,SAAS,iBACRlB,OAAA;YAAKoD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrD,OAAA;cAAAqD,QAAA,gBAAGrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvC,SAAS,CAAC+C,aAAa,IAAI,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEzD,OAAA;cAAAqD,QAAA,gBAAGrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIS,IAAI,CAAChD,SAAS,CAACiD,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAAqD,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBzD,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrD,OAAA;UAAAqD,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxBzD,OAAA;UACEqE,IAAI,EAAC,MAAM;UACXC,KAAK,EAAElD,QAAS;UAChBmD,QAAQ,EAAGC,CAAC,IAAKnD,WAAW,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC7CI,QAAQ,EAAE9D,OAAQ;UAClB+D,WAAW,EAAC;QAAsB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACFzD,OAAA;UAAAqD,QAAA,EAAO;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BzD,OAAA;UACEqE,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEhD,KAAM;UACbiD,QAAQ,EAAGC,CAAC,IAAKjD,QAAQ,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CI,QAAQ,EAAE9D,OAAQ;UAClB+D,WAAW,EAAC;QAA0B;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACFzD,OAAA;UACEoD,SAAS,EAAC,eAAe;UACzBM,OAAO,EAAEhB,mBAAoB;UAC7BgC,QAAQ,EAAE9D,OAAQ;UAAAyC,QAAA,EAEjBzC,OAAO,GAAG,aAAa,GAAG;QAAgB;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzD,OAAA;QAAAqD,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBzD,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrD,OAAA;UAAAqD,QAAA,EAAO;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/BzD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YACEqE,IAAI,EAAEvC,aAAa,CAACE,OAAO,GAAG,MAAM,GAAG,UAAW;YAClDsC,KAAK,EAAE9C,eAAgB;YACvB+C,QAAQ,EAAGC,CAAC,IAAK/C,kBAAkB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACpDI,QAAQ,EAAE9D,OAAQ;YAClB+D,WAAW,EAAC;UAAwB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFzD,OAAA;YAAM0D,OAAO,EAAEA,CAAA,KAAMZ,cAAc,CAAC,SAAS,CAAE;YAACM,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENzD,OAAA;UAAAqD,QAAA,EAAO;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3BzD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YACEqE,IAAI,EAAEvC,aAAa,CAACG,GAAG,GAAG,MAAM,GAAG,UAAW;YAC9CqC,KAAK,EAAE5C,WAAY;YACnB6C,QAAQ,EAAGC,CAAC,IAAK7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,QAAQ,EAAE9D,OAAQ;YAClB+D,WAAW,EAAC;UAAuC;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFzD,OAAA;YAAM0D,OAAO,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;YAACM,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAENzD,OAAA;UAAAqD,QAAA,EAAO;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnCzD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YACEqE,IAAI,EAAEvC,aAAa,CAACI,OAAO,GAAG,MAAM,GAAG,UAAW;YAClDoC,KAAK,EAAE1C,eAAgB;YACvB2C,QAAQ,EAAGC,CAAC,IAAK3C,kBAAkB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACpDI,QAAQ,EAAE9D,OAAQ;YAClB+D,WAAW,EAAC;UAAsB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFzD,OAAA;YAAM0D,OAAO,EAAEA,CAAA,KAAMZ,cAAc,CAAC,SAAS,CAAE;YAACM,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENzD,OAAA;UACEoD,SAAS,EAAC,eAAe;UACzBM,OAAO,EAAET,oBAAqB;UAC9ByB,QAAQ,EAAE9D,OAAQ;UAAAyC,QAAA,EAEjBzC,OAAO,GAAG,aAAa,GAAG;QAAiB;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENzD,OAAA;MAAKoD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrD,OAAA;QAAAqD,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3BzD,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BzD,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,eAAAzD,OAAA;cAAMoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrFzD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtD,IAAI,CAACoC,EAAE;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CzD,OAAA;YAAAqD,QAAA,gBAAGrD,OAAA;cAAAqD,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtD,IAAI,CAAC6D,IAAI;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBzD,OAAA;UAAAqD,QAAA,EAAG;QAAuE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9EzD,OAAA;UAAKoD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOqE,IAAI,EAAC,UAAU;cAACO,cAAc;cAACF,QAAQ;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,+CAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOqE,IAAI,EAAC,UAAU;cAACO,cAAc;cAACF,QAAQ;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrD,OAAA;UAAAqD,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBzD,OAAA;UAAAqD,QAAA,EAAG;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzDzD,OAAA;UACEoD,SAAS,EAAC,eAAe;UACzBM,OAAO,EAAEP,YAAa;UAAAE,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzD,OAAA,CAACF,MAAM;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvD,EAAA,CA5TID,QAAQ;EAAA,QACiEN,OAAO;AAAA;AAAAkF,EAAA,GADhF5E,QAAQ;AA8Td,eAAeA,QAAQ;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}