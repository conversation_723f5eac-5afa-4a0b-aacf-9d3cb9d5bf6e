import React, { useState } from 'react';
import Login from './Login';
import Signup from './Signup';
import './Auth.css';

const AuthModal = ({ isOpen, onClose }) => {
  const [currentView, setCurrentView] = useState('login'); // 'login' or 'signup'

  if (!isOpen) return null;

  const handleSwitchToSignup = () => {
    setCurrentView('signup');
  };

  const handleSwitchToLogin = () => {
    setCurrentView('login');
  };

  const handleClose = () => {
    setCurrentView('login'); // Reset to login view
    onClose();
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return (
    <div className="auth-modal" onClick={handleBackdropClick}>
      <div className="auth-modal-content">
        <button className="auth-close-button" onClick={handleClose}>
          ×
        </button>
        
        {currentView === 'login' ? (
          <Login 
            onSwitchToSignup={handleSwitchToSignup}
            onClose={handleClose}
          />
        ) : (
          <Signup 
            onSwitchToLogin={handleSwitchToLogin}
            onClose={handleClose}
          />
        )}
      </div>
    </div>
  );
};

export default AuthModal;
