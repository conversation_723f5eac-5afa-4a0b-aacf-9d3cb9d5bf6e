{"ast": null, "code": "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nfunction resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}\nexport default resolveComponentProps;", "map": {"version": 3, "names": ["resolveComponentProps", "componentProps", "ownerState", "slotState"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/utils/esm/resolveComponentProps/resolveComponentProps.js"], "sourcesContent": ["/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nfunction resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}\nexport default resolveComponentProps;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqBA,CAACC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACpE,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxC,OAAOA,cAAc,CAACC,UAAU,EAAEC,SAAS,CAAC;EAC9C;EACA,OAAOF,cAAc;AACvB;AACA,eAAeD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}