{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\MedicalQuery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from './contexts/AuthContext';\nimport apiService from './services/api';\nimport AuthModal from './components/AuthModal';\nimport './MedicalQuery.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MedicalQuery = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [inputMethod, setInputMethod] = useState('Text');\n  const [isRecording, setIsRecording] = useState(false);\n  const [query, setQuery] = useState('');\n  const [imageFile, setImageFile] = useState(null);\n  const [pdfFile, setPdfFile] = useState(null);\n  const [response, setResponse] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  const [selectedRole, setSelectedRole] = useState('Common User');\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\n  const handleSubmit = useCallback(async () => {\n    // Check if user is authenticated\n    if (!isAuthenticated) {\n      setShowAuthModal(true);\n      return;\n    }\n    if (!query && !imageFile && !pdfFile) {\n      setError('Please provide a question or upload a medical image/document.');\n      return;\n    }\n    setError('');\n    setResponse('');\n    setIsLoading(true);\n    try {\n      let result;\n      if (imageFile || pdfFile) {\n        // Use file upload endpoint\n        const formData = new FormData();\n        formData.append('user_id', user.id);\n        formData.append('query', query || 'Please analyze this medical document/image');\n        formData.append('role', user.role || selectedRole);\n        formData.append('language', selectedLanguage);\n        if (imageFile) {\n          formData.append('file', imageFile);\n        } else if (pdfFile) {\n          formData.append('file', pdfFile);\n        }\n        result = await apiService.submitQueryWithFile(formData);\n      } else {\n        // Use text-only endpoint\n        result = await apiService.submitQuery({\n          user_id: user.id,\n          query: query,\n          role: user.role || selectedRole,\n          language: selectedLanguage,\n          has_image: false\n        });\n      }\n      setResponse(result.response);\n    } catch (err) {\n      console.error('Query submission error:', err);\n      setError(err.message || 'Failed to submit query. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [query, imageFile, pdfFile, isAuthenticated, user, selectedRole, selectedLanguage]);\n  useEffect(() => {\n    const rerun = localStorage.getItem('rerunQuery');\n    if (rerun) {\n      const parsed = JSON.parse(rerun);\n      setQuery(parsed.query);\n      setTimeout(() => {\n        handleSubmit();\n        localStorage.removeItem('rerunQuery');\n      }, 500);\n    }\n  }, [handleSubmit]);\n  const handleStartRecording = () => {\n    setIsRecording(true);\n    if ('webkitSpeechRecognition' in window) {\n      const recognition = new window.webkitSpeechRecognition();\n      recognition.lang = 'en-US';\n      recognition.onresult = event => {\n        const transcript = event.results[0][0].transcript;\n        setQuery(transcript);\n        setIsRecording(false);\n      };\n      recognition.onerror = () => {\n        alert('Voice recognition error.');\n        setIsRecording(false);\n      };\n      recognition.start();\n    } else {\n      alert('Voice recognition not supported in this browser.');\n      setIsRecording(false);\n    }\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const allowedImageTypes = ['image/png', 'image/jpeg', 'image/jpg'];\n    const allowedDocTypes = ['application/pdf'];\n    if (allowedImageTypes.includes(file.type)) {\n      setImageFile(file);\n      setPdfFile(null);\n      setError('');\n    } else if (allowedDocTypes.includes(file.type)) {\n      setPdfFile(file);\n      setImageFile(null);\n      setError('');\n    } else {\n      setImageFile(null);\n      setPdfFile(null);\n      setError('Only PNG, JPG, JPEG, and PDF files are allowed.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"medical-query-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"What would you like to know?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-info\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Welcome, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 23\n        }, this), \" (\", user.role, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selection-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Role:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedRole,\n          onChange: e => setSelectedRole(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Common User\",\n            children: \"Common User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Student\",\n            children: \"Medical Student\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Doctor\",\n            children: \"Doctor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Language:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLanguage,\n          onChange: e => setSelectedLanguage(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"English\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"French\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"German\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Hindi\",\n            children: \"Hindi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Input Method:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"method-toggle\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: inputMethod === 'Text' ? 'selected' : '',\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"radio\",\n          name: \"method\",\n          value: \"Text\",\n          checked: inputMethod === 'Text',\n          onChange: () => setInputMethod('Text')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), \" Text\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: inputMethod === 'Voice' ? 'selected' : '',\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"radio\",\n          name: \"method\",\n          value: \"Voice\",\n          checked: inputMethod === 'Voice',\n          onChange: () => setInputMethod('Voice')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), \" Voice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), inputMethod === 'Text' ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n      className: \"text-box\",\n      placeholder: \"Enter your medical query here...\",\n      value: query,\n      onChange: e => setQuery(e.target.value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"voice-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleStartRecording,\n        className: \"start-btn\",\n        children: \"\\uD83C\\uDFA4 Start Recording\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), isRecording && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"recording-text\",\n        children: \"\\uD83C\\uDF99\\uFE0F Listening...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"folder-icon\",\n        children: \"\\uD83D\\uDCC1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Upload Medical Document\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload X-rays, lab results, medical scans, PDFs, or any relevant documents\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".png,.jpg,.jpeg,.pdf\",\n        className: \"file-upload\",\n        onChange: handleFileChange,\n        disabled: isLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), imageFile && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Selected Image:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 26\n        }, this), \" \", imageFile.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 23\n      }, this), pdfFile && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Selected PDF:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 24\n        }, this), \" \", pdfFile.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"analyze-btn\",\n      onClick: handleSubmit,\n      disabled: isLoading,\n      children: isLoading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), response && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"response-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Medical Analysis:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        dangerouslySetInnerHTML: {\n          __html: response.replace(/\\n/g, '<br/>')\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AuthModal, {\n      isOpen: showAuthModal,\n      onClose: () => setShowAuthModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(MedicalQuery, \"NMtG/JdeWFy7hbimQyRgr9SNNTc=\", false, function () {\n  return [useAuth];\n});\n_c = MedicalQuery;\nexport default MedicalQuery;\nvar _c;\n$RefreshReg$(_c, \"MedicalQuery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useAuth", "apiService", "AuthModal", "jsxDEV", "_jsxDEV", "Medical<PERSON><PERSON>y", "_s", "user", "isAuthenticated", "inputMethod", "setInputMethod", "isRecording", "setIsRecording", "query", "<PERSON><PERSON><PERSON><PERSON>", "imageFile", "setImageFile", "pdfFile", "setPdfFile", "response", "setResponse", "error", "setError", "isLoading", "setIsLoading", "showAuthModal", "setShowAuthModal", "selectedR<PERSON>", "setSelectedRole", "selectedLanguage", "setSelectedLanguage", "handleSubmit", "result", "formData", "FormData", "append", "id", "role", "submitQueryWithFile", "submitQuery", "user_id", "language", "has_image", "err", "console", "message", "rerun", "localStorage", "getItem", "parsed", "JSON", "parse", "setTimeout", "removeItem", "handleStartRecording", "window", "recognition", "webkitSpeechRecognition", "lang", "on<PERSON>ult", "event", "transcript", "results", "onerror", "alert", "start", "handleFileChange", "e", "file", "target", "files", "allowedImageTypes", "allowedDocTypes", "includes", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "value", "onChange", "checked", "placeholder", "onClick", "accept", "disabled", "dangerouslySetInnerHTML", "__html", "replace", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/MedicalQuery.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from './contexts/AuthContext';\r\nimport apiService from './services/api';\r\nimport AuthModal from './components/AuthModal';\r\nimport './MedicalQuery.css';\r\n\r\nconst MedicalQuery = () => {\r\n  const { user, isAuthenticated } = useAuth();\r\n  const [inputMethod, setInputMethod] = useState('Text');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [query, setQuery] = useState('');\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [pdfFile, setPdfFile] = useState(null);\r\n  const [response, setResponse] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showAuthModal, setShowAuthModal] = useState(false);\r\n  const [selectedRole, setSelectedRole] = useState('Common User');\r\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\r\n\r\n  const handleSubmit = useCallback(async () => {\r\n    // Check if user is authenticated\r\n    if (!isAuthenticated) {\r\n      setShowAuthModal(true);\r\n      return;\r\n    }\r\n\r\n    if (!query && !imageFile && !pdfFile) {\r\n      setError('Please provide a question or upload a medical image/document.');\r\n      return;\r\n    }\r\n\r\n    setError('');\r\n    setResponse('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let result;\r\n\r\n      if (imageFile || pdfFile) {\r\n        // Use file upload endpoint\r\n        const formData = new FormData();\r\n        formData.append('user_id', user.id);\r\n        formData.append('query', query || 'Please analyze this medical document/image');\r\n        formData.append('role', user.role || selectedRole);\r\n        formData.append('language', selectedLanguage);\r\n\r\n        if (imageFile) {\r\n          formData.append('file', imageFile);\r\n        } else if (pdfFile) {\r\n          formData.append('file', pdfFile);\r\n        }\r\n\r\n        result = await apiService.submitQueryWithFile(formData);\r\n      } else {\r\n        // Use text-only endpoint\r\n        result = await apiService.submitQuery({\r\n          user_id: user.id,\r\n          query: query,\r\n          role: user.role || selectedRole,\r\n          language: selectedLanguage,\r\n          has_image: false\r\n        });\r\n      }\r\n\r\n      setResponse(result.response);\r\n    } catch (err) {\r\n      console.error('Query submission error:', err);\r\n      setError(err.message || 'Failed to submit query. Please try again.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [query, imageFile, pdfFile, isAuthenticated, user, selectedRole, selectedLanguage]);\r\n\r\n  useEffect(() => {\r\n    const rerun = localStorage.getItem('rerunQuery');\r\n    if (rerun) {\r\n      const parsed = JSON.parse(rerun);\r\n      setQuery(parsed.query);\r\n      setTimeout(() => {\r\n        handleSubmit();\r\n        localStorage.removeItem('rerunQuery');\r\n      }, 500);\r\n    }\r\n  }, [handleSubmit]);\r\n\r\n  const handleStartRecording = () => {\r\n    setIsRecording(true);\r\n    if ('webkitSpeechRecognition' in window) {\r\n      const recognition = new window.webkitSpeechRecognition();\r\n      recognition.lang = 'en-US';\r\n      recognition.onresult = (event) => {\r\n        const transcript = event.results[0][0].transcript;\r\n        setQuery(transcript);\r\n        setIsRecording(false);\r\n      };\r\n      recognition.onerror = () => {\r\n        alert('Voice recognition error.');\r\n        setIsRecording(false);\r\n      };\r\n      recognition.start();\r\n    } else {\r\n      alert('Voice recognition not supported in this browser.');\r\n      setIsRecording(false);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    const allowedImageTypes = ['image/png', 'image/jpeg', 'image/jpg'];\r\n    const allowedDocTypes = ['application/pdf'];\r\n\r\n    if (allowedImageTypes.includes(file.type)) {\r\n      setImageFile(file);\r\n      setPdfFile(null);\r\n      setError('');\r\n    } else if (allowedDocTypes.includes(file.type)) {\r\n      setPdfFile(file);\r\n      setImageFile(null);\r\n      setError('');\r\n    } else {\r\n      setImageFile(null);\r\n      setPdfFile(null);\r\n      setError('Only PNG, JPG, JPEG, and PDF files are allowed.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"medical-query-wrapper\">\r\n      <h2>What would you like to know?</h2>\r\n\r\n      {isAuthenticated && (\r\n        <div className=\"user-info\">\r\n          <p>Welcome, <strong>{user.name}</strong> ({user.role})</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Role and Language Selection for non-authenticated users */}\r\n      {!isAuthenticated && (\r\n        <div className=\"selection-section\">\r\n          <div className=\"form-group\">\r\n            <label>Role:</label>\r\n            <select value={selectedRole} onChange={(e) => setSelectedRole(e.target.value)}>\r\n              <option value=\"Common User\">Common User</option>\r\n              <option value=\"Student\">Medical Student</option>\r\n              <option value=\"Doctor\">Doctor</option>\r\n            </select>\r\n          </div>\r\n          <div className=\"form-group\">\r\n            <label>Language:</label>\r\n            <select value={selectedLanguage} onChange={(e) => setSelectedLanguage(e.target.value)}>\r\n              <option value=\"English\">English</option>\r\n              <option value=\"Spanish\">Spanish</option>\r\n              <option value=\"French\">French</option>\r\n              <option value=\"German\">German</option>\r\n              <option value=\"Hindi\">Hindi</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <p><strong>Input Method:</strong></p>\r\n\r\n      <div className=\"method-toggle\">\r\n        <label className={inputMethod === 'Text' ? 'selected' : ''}>\r\n          <input\r\n            type=\"radio\"\r\n            name=\"method\"\r\n            value=\"Text\"\r\n            checked={inputMethod === 'Text'}\r\n            onChange={() => setInputMethod('Text')}\r\n          /> Text\r\n        </label>\r\n        <label className={inputMethod === 'Voice' ? 'selected' : ''}>\r\n          <input\r\n            type=\"radio\"\r\n            name=\"method\"\r\n            value=\"Voice\"\r\n            checked={inputMethod === 'Voice'}\r\n            onChange={() => setInputMethod('Voice')}\r\n          /> Voice\r\n        </label>\r\n      </div>\r\n\r\n      {inputMethod === 'Text' ? (\r\n        <textarea\r\n          className=\"text-box\"\r\n          placeholder=\"Enter your medical query here...\"\r\n          value={query}\r\n          onChange={(e) => setQuery(e.target.value)}\r\n        />\r\n      ) : (\r\n        <div className=\"voice-box\">\r\n          <button onClick={handleStartRecording} className=\"start-btn\">🎤 Start Recording</button>\r\n          {isRecording && <p className=\"recording-text\">🎙️ Listening...</p>}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"upload-section\">\r\n        <div className=\"folder-icon\">📁</div>\r\n        <h3>Upload Medical Document</h3>\r\n        <p>Upload X-rays, lab results, medical scans, PDFs, or any relevant documents</p>\r\n        <input\r\n          type=\"file\"\r\n          accept=\".png,.jpg,.jpeg,.pdf\"\r\n          className=\"file-upload\"\r\n          onChange={handleFileChange}\r\n          disabled={isLoading}\r\n        />\r\n        {imageFile && <p><strong>Selected Image:</strong> {imageFile.name}</p>}\r\n        {pdfFile && <p><strong>Selected PDF:</strong> {pdfFile.name}</p>}\r\n      </div>\r\n\r\n      {error && <p className=\"error-message\">{error}</p>}\r\n\r\n      <button\r\n        className=\"analyze-btn\"\r\n        onClick={handleSubmit}\r\n        disabled={isLoading}\r\n      >\r\n        {isLoading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'}\r\n      </button>\r\n\r\n      {response && (\r\n        <div className=\"response-box\">\r\n          <h3>Medical Analysis:</h3>\r\n          <div dangerouslySetInnerHTML={{ __html: response.replace(/\\n/g, '<br/>') }} />\r\n        </div>\r\n      )}\r\n\r\n      <AuthModal\r\n        isOpen={showAuthModal}\r\n        onClose={() => setShowAuthModal(false)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MedicalQuery;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,aAAa,CAAC;EAC/D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,SAAS,CAAC;EAEnE,MAAMkC,YAAY,GAAGhC,WAAW,CAAC,YAAY;IAC3C;IACA,IAAI,CAACS,eAAe,EAAE;MACpBkB,gBAAgB,CAAC,IAAI,CAAC;MACtB;IACF;IAEA,IAAI,CAACb,KAAK,IAAI,CAACE,SAAS,IAAI,CAACE,OAAO,EAAE;MACpCK,QAAQ,CAAC,+DAA+D,CAAC;MACzE;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC;IACZF,WAAW,CAAC,EAAE,CAAC;IACfI,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIQ,MAAM;MAEV,IAAIjB,SAAS,IAAIE,OAAO,EAAE;QACxB;QACA,MAAMgB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE5B,IAAI,CAAC6B,EAAE,CAAC;QACnCH,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEtB,KAAK,IAAI,4CAA4C,CAAC;QAC/EoB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC8B,IAAI,IAAIV,YAAY,CAAC;QAClDM,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEN,gBAAgB,CAAC;QAE7C,IAAId,SAAS,EAAE;UACbkB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpB,SAAS,CAAC;QACpC,CAAC,MAAM,IAAIE,OAAO,EAAE;UAClBgB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElB,OAAO,CAAC;QAClC;QAEAe,MAAM,GAAG,MAAM/B,UAAU,CAACqC,mBAAmB,CAACL,QAAQ,CAAC;MACzD,CAAC,MAAM;QACL;QACAD,MAAM,GAAG,MAAM/B,UAAU,CAACsC,WAAW,CAAC;UACpCC,OAAO,EAAEjC,IAAI,CAAC6B,EAAE;UAChBvB,KAAK,EAAEA,KAAK;UACZwB,IAAI,EAAE9B,IAAI,CAAC8B,IAAI,IAAIV,YAAY;UAC/Bc,QAAQ,EAAEZ,gBAAgB;UAC1Ba,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MAEAtB,WAAW,CAACY,MAAM,CAACb,QAAQ,CAAC;IAC9B,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,yBAAyB,EAAEsB,GAAG,CAAC;MAC7CrB,QAAQ,CAACqB,GAAG,CAACE,OAAO,IAAI,2CAA2C,CAAC;IACtE,CAAC,SAAS;MACRrB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACX,KAAK,EAAEE,SAAS,EAAEE,OAAO,EAAET,eAAe,EAAED,IAAI,EAAEoB,YAAY,EAAEE,gBAAgB,CAAC,CAAC;EAEtF/B,SAAS,CAAC,MAAM;IACd,MAAMgD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIF,KAAK,EAAE;MACT,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;MAChChC,QAAQ,CAACmC,MAAM,CAACpC,KAAK,CAAC;MACtBuC,UAAU,CAAC,MAAM;QACfrB,YAAY,CAAC,CAAC;QACdgB,YAAY,CAACM,UAAU,CAAC,YAAY,CAAC;MACvC,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAACtB,YAAY,CAAC,CAAC;EAElB,MAAMuB,oBAAoB,GAAGA,CAAA,KAAM;IACjC1C,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI,yBAAyB,IAAI2C,MAAM,EAAE;MACvC,MAAMC,WAAW,GAAG,IAAID,MAAM,CAACE,uBAAuB,CAAC,CAAC;MACxDD,WAAW,CAACE,IAAI,GAAG,OAAO;MAC1BF,WAAW,CAACG,QAAQ,GAAIC,KAAK,IAAK;QAChC,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,UAAU;QACjD/C,QAAQ,CAAC+C,UAAU,CAAC;QACpBjD,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MACD4C,WAAW,CAACO,OAAO,GAAG,MAAM;QAC1BC,KAAK,CAAC,0BAA0B,CAAC;QACjCpD,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MACD4C,WAAW,CAACS,KAAK,CAAC,CAAC;IACrB,CAAC,MAAM;MACLD,KAAK,CAAC,kDAAkD,CAAC;MACzDpD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsD,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAClE,MAAMC,eAAe,GAAG,CAAC,iBAAiB,CAAC;IAE3C,IAAID,iBAAiB,CAACE,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;MACzC1D,YAAY,CAACoD,IAAI,CAAC;MAClBlD,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,MAAM,IAAIkD,eAAe,CAACC,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;MAC9CxD,UAAU,CAACkD,IAAI,CAAC;MAChBpD,YAAY,CAAC,IAAI,CAAC;MAClBM,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,MAAM;MACLN,YAAY,CAAC,IAAI,CAAC;MAClBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,iDAAiD,CAAC;IAC7D;EACF,CAAC;EAED,oBACElB,OAAA;IAAKuE,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCxE,OAAA;MAAAwE,QAAA,EAAI;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEpCxE,eAAe,iBACdJ,OAAA;MAAKuE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBxE,OAAA;QAAAwE,QAAA,GAAG,WAAS,eAAAxE,OAAA;UAAAwE,QAAA,EAASrE,IAAI,CAAC0E;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,MAAE,EAACzE,IAAI,CAAC8B,IAAI,EAAC,GAAC;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CACN,EAGA,CAACxE,eAAe,iBACfJ,OAAA;MAAKuE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxE,OAAA;QAAKuE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxE,OAAA;UAAAwE,QAAA,EAAO;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpB5E,OAAA;UAAQ8E,KAAK,EAAEvD,YAAa;UAACwD,QAAQ,EAAGhB,CAAC,IAAKvC,eAAe,CAACuC,CAAC,CAACE,MAAM,CAACa,KAAK,CAAE;UAAAN,QAAA,gBAC5ExE,OAAA;YAAQ8E,KAAK,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChD5E,OAAA;YAAQ8E,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChD5E,OAAA;YAAQ8E,KAAK,EAAC,QAAQ;YAAAN,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5E,OAAA;QAAKuE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxE,OAAA;UAAAwE,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB5E,OAAA;UAAQ8E,KAAK,EAAErD,gBAAiB;UAACsD,QAAQ,EAAGhB,CAAC,IAAKrC,mBAAmB,CAACqC,CAAC,CAACE,MAAM,CAACa,KAAK,CAAE;UAAAN,QAAA,gBACpFxE,OAAA;YAAQ8E,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5E,OAAA;YAAQ8E,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5E,OAAA;YAAQ8E,KAAK,EAAC,QAAQ;YAAAN,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC5E,OAAA;YAAQ8E,KAAK,EAAC,QAAQ;YAAAN,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC5E,OAAA;YAAQ8E,KAAK,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5E,OAAA;MAAAwE,QAAA,eAAGxE,OAAA;QAAAwE,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAErC5E,OAAA;MAAKuE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxE,OAAA;QAAOuE,SAAS,EAAElE,WAAW,KAAK,MAAM,GAAG,UAAU,GAAG,EAAG;QAAAmE,QAAA,gBACzDxE,OAAA;UACEsE,IAAI,EAAC,OAAO;UACZO,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,MAAM;UACZE,OAAO,EAAE3E,WAAW,KAAK,MAAO;UAChC0E,QAAQ,EAAEA,CAAA,KAAMzE,cAAc,CAAC,MAAM;QAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,SACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5E,OAAA;QAAOuE,SAAS,EAAElE,WAAW,KAAK,OAAO,GAAG,UAAU,GAAG,EAAG;QAAAmE,QAAA,gBAC1DxE,OAAA;UACEsE,IAAI,EAAC,OAAO;UACZO,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,OAAO;UACbE,OAAO,EAAE3E,WAAW,KAAK,OAAQ;UACjC0E,QAAQ,EAAEA,CAAA,KAAMzE,cAAc,CAAC,OAAO;QAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,UACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELvE,WAAW,KAAK,MAAM,gBACrBL,OAAA;MACEuE,SAAS,EAAC,UAAU;MACpBU,WAAW,EAAC,kCAAkC;MAC9CH,KAAK,EAAErE,KAAM;MACbsE,QAAQ,EAAGhB,CAAC,IAAKrD,QAAQ,CAACqD,CAAC,CAACE,MAAM,CAACa,KAAK;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,gBAEF5E,OAAA;MAAKuE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxE,OAAA;QAAQkF,OAAO,EAAEhC,oBAAqB;QAACqB,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACvFrE,WAAW,iBAAIP,OAAA;QAAGuE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN,eAED5E,OAAA;MAAKuE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxE,OAAA;QAAKuE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrC5E,OAAA;QAAAwE,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC5E,OAAA;QAAAwE,QAAA,EAAG;MAA0E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACjF5E,OAAA;QACEsE,IAAI,EAAC,MAAM;QACXa,MAAM,EAAC,sBAAsB;QAC7BZ,SAAS,EAAC,aAAa;QACvBQ,QAAQ,EAAEjB,gBAAiB;QAC3BsB,QAAQ,EAAEjE;MAAU;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,EACDjE,SAAS,iBAAIX,OAAA;QAAAwE,QAAA,gBAAGxE,OAAA;UAAAwE,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACjE,SAAS,CAACkE,IAAI;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACrE/D,OAAO,iBAAIb,OAAA;QAAAwE,QAAA,gBAAGxE,OAAA;UAAAwE,QAAA,EAAQ;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC/D,OAAO,CAACgE,IAAI;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EAEL3D,KAAK,iBAAIjB,OAAA;MAAGuE,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEvD;IAAK;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElD5E,OAAA;MACEuE,SAAS,EAAC,aAAa;MACvBW,OAAO,EAAEvD,YAAa;MACtByD,QAAQ,EAAEjE,SAAU;MAAAqD,QAAA,EAEnBrD,SAAS,GAAG,iBAAiB,GAAG;IAA8B;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,EAER7D,QAAQ,iBACPf,OAAA;MAAKuE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxE,OAAA;QAAAwE,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B5E,OAAA;QAAKqF,uBAAuB,EAAE;UAAEC,MAAM,EAAEvE,QAAQ,CAACwE,OAAO,CAAC,KAAK,EAAE,OAAO;QAAE;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CACN,eAED5E,OAAA,CAACF,SAAS;MACR0F,MAAM,EAAEnE,aAAc;MACtBoE,OAAO,EAAEA,CAAA,KAAMnE,gBAAgB,CAAC,KAAK;IAAE;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAxOID,YAAY;EAAA,QACkBL,OAAO;AAAA;AAAA8F,EAAA,GADrCzF,YAAY;AA0OlB,eAAeA,YAAY;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}