[{"C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Settings.js": "3", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RightScreen.js": "4", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\QueryHistory.js": "5", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\LeftScreen.js": "6", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedcodeBanner.js": "7", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Footer.js": "8", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RoleSelector.js": "9", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\WelcomeCard.js": "10", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedicalQuery.js": "11", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\ActivityDashboard.js": "12"}, {"size": 347, "mtime": 1751224837688, "results": "13", "hashOfConfig": "14"}, {"size": 1998, "mtime": 1751224837475, "results": "15", "hashOfConfig": "14"}, {"size": 5774, "mtime": 1751224838255, "results": "16", "hashOfConfig": "14"}, {"size": 568, "mtime": 1751224838100, "results": "17", "hashOfConfig": "14"}, {"size": 1452, "mtime": 1751224838022, "results": "18", "hashOfConfig": "14"}, {"size": 2246, "mtime": 1751224837771, "results": "19", "hashOfConfig": "14"}, {"size": 994, "mtime": 1751224837856, "results": "20", "hashOfConfig": "14"}, {"size": 753, "mtime": 1751224837606, "results": "21", "hashOfConfig": "14"}, {"size": 1421, "mtime": 1751224838178, "results": "22", "hashOfConfig": "14"}, {"size": 715, "mtime": 1751224838370, "results": "23", "hashOfConfig": "14"}, {"size": 4722, "mtime": 1751224837940, "results": "24", "hashOfConfig": "14"}, {"size": 1071, "mtime": 1751224837399, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1emmwot", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Settings.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RightScreen.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\QueryHistory.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\LeftScreen.js", ["62", "63"], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedcodeBanner.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Footer.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RoleSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\WelcomeCard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedicalQuery.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\ActivityDashboard.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 2, "column": 17, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 25}, {"ruleId": "64", "severity": 1, "message": "68", "line": 4, "column": 8, "nodeType": "66", "messageId": "67", "endLine": 4, "endColumn": 16}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'MenuIcon' is defined but never used."]