[{"C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Settings.js": "3", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RightScreen.js": "4", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\QueryHistory.js": "5", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\LeftScreen.js": "6", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedcodeBanner.js": "7", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Footer.js": "8", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RoleSelector.js": "9", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\WelcomeCard.js": "10", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedicalQuery.js": "11", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\ActivityDashboard.js": "12", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\contexts\\AuthContext.js": "13", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\AuthModal.js": "14", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\Login.js": "16", "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\Signup.js": "17"}, {"size": 347, "mtime": 1751224837688, "results": "18", "hashOfConfig": "19"}, {"size": 2116, "mtime": 1751299550832, "results": "20", "hashOfConfig": "19"}, {"size": 10247, "mtime": 1751299510200, "results": "21", "hashOfConfig": "19"}, {"size": 568, "mtime": 1751224838100, "results": "22", "hashOfConfig": "19"}, {"size": 4511, "mtime": 1751299343449, "results": "23", "hashOfConfig": "19"}, {"size": 2246, "mtime": 1751224837771, "results": "24", "hashOfConfig": "19"}, {"size": 994, "mtime": 1751224837856, "results": "25", "hashOfConfig": "19"}, {"size": 753, "mtime": 1751224837606, "results": "26", "hashOfConfig": "19"}, {"size": 1421, "mtime": 1751224838178, "results": "27", "hashOfConfig": "19"}, {"size": 715, "mtime": 1751224838370, "results": "28", "hashOfConfig": "19"}, {"size": 7964, "mtime": 1751299270436, "results": "29", "hashOfConfig": "19"}, {"size": 1071, "mtime": 1751224837399, "results": "30", "hashOfConfig": "19"}, {"size": 3972, "mtime": 1751299046948, "results": "31", "hashOfConfig": "19"}, {"size": 1223, "mtime": 1751299143612, "results": "32", "hashOfConfig": "19"}, {"size": 4199, "mtime": 1751299023672, "results": "33", "hashOfConfig": "19"}, {"size": 2807, "mtime": 1751299071409, "results": "34", "hashOfConfig": "19"}, {"size": 5859, "mtime": 1751299099096, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1emmwot", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Settings.js", ["87"], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RightScreen.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\QueryHistory.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\LeftScreen.js", ["88", "89"], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedcodeBanner.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\Footer.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\RoleSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\WelcomeCard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\MedicalQuery.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\ActivityDashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\AuthModal.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Med-cure\\Med-cure\\medcure\\src\\components\\Signup.js", [], [], {"ruleId": "90", "severity": 1, "message": "91", "line": 38, "column": 6, "nodeType": "92", "endLine": 38, "endColumn": 12, "suggestions": "93"}, {"ruleId": "94", "severity": 1, "message": "95", "line": 2, "column": 17, "nodeType": "96", "messageId": "97", "endLine": 2, "endColumn": 25}, {"ruleId": "94", "severity": 1, "message": "98", "line": 4, "column": 8, "nodeType": "96", "messageId": "97", "endLine": 4, "endColumn": 16}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserStats'. Either include it or remove the dependency array.", "ArrayExpression", ["99"], "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'MenuIcon' is defined but never used.", {"desc": "100", "fix": "101"}, "Update the dependencies array to be: [fetchUserStats, user]", {"range": "102", "text": "103"}, [1336, 1342], "[fetchUserStats, user]"]