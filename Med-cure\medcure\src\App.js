import './App.css';
import LeftScreen from './LeftScreen';
import RightScreen from './RightScreen';
import QueryHistory from './QueryHistory';
import Settings from './Settings';
import MedcodeBanner from './MedcodeBanner';
import { AuthProvider } from './contexts/AuthContext';
import { useState, useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';

function Layout({ sidebarOpen, toggleSidebar, isMobile }) {
  const location = useLocation();

  const getPageComponent = () => {
    switch (location.pathname) {
      case '/query-history':
        return <QueryHistory />;
      case '/settings':
        return <Settings />;
      default:
        return <RightScreen />;
    }
  };

  return (
    <div className="App">
      {isMobile && (
        <button className="hamburger" onClick={toggleSidebar}>
          <span></span>
          <span></span>
          <span></span>
        </button>
      )}

      <div className={`left-side ${sidebarOpen ? 'open' : ''}`}>
        <LeftScreen />
      </div>

      <div className={`right-side ${sidebarOpen && isMobile ? 'blurred' : ''}`}>
        <MedcodeBanner />
        {getPageComponent()}
      </div>
    </div>
  );
}

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 1024;
      setIsMobile(mobile);
      if (!mobile) setSidebarOpen(false);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <AuthProvider>
      <Routes>
        <Route
          path="*"
          element={
            <Layout
              sidebarOpen={sidebarOpen}
              toggleSidebar={toggleSidebar}
              isMobile={isMobile}
            />
          }
        />
      </Routes>
    </AuthProvider>
  );
}

export default App; // ✅ make sure this line exists
