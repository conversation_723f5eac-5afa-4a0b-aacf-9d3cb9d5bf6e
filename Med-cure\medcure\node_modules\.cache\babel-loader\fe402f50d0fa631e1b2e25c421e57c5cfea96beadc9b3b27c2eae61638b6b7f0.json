{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\MedcodeBanner.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport './MedcodeBanner.css';\nimport { useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MedcodeBanner = () => {\n  _s();\n  const location = useLocation();\n\n  // Map routes to banner content\n  const bannerContent = {\n    '/': {\n      title: 'MedCode AI 🩺',\n      subtitle: 'Your AI-powered medical assistant for personalized healthcare insights'\n    },\n    '/query-history': {\n      title: 'Query History 📜',\n      subtitle: 'Explore your previous health queries and insights'\n    },\n    '/settings': {\n      title: 'Settings ⚙️',\n      subtitle: 'Manage your profile and preferences'\n    }\n  };\n  const {\n    title,\n    subtitle\n  } = bannerContent[location.pathname] || bannerContent['/'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"banner-wrapper\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"banner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"banner-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"banner-subtitle\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(MedcodeBanner, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = MedcodeBanner;\nexport default MedcodeBanner;\nvar _c;\n$RefreshReg$(_c, \"MedcodeBanner\");", "map": {"version": 3, "names": ["React", "useLocation", "jsxDEV", "_jsxDEV", "MedcodeBanner", "_s", "location", "bannerContent", "title", "subtitle", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/MedcodeBanner.js"], "sourcesContent": ["import React from 'react';\r\nimport './MedcodeBanner.css';\r\nimport { useLocation } from 'react-router-dom';\r\n\r\nconst MedcodeBanner = () => {\r\n  const location = useLocation();\r\n\r\n  // Map routes to banner content\r\n  const bannerContent = {\r\n    '/': {\r\n      title: 'MedCode AI 🩺',\r\n      subtitle: 'Your AI-powered medical assistant for personalized healthcare insights',\r\n    },\r\n    '/query-history': {\r\n      title: 'Query History 📜',\r\n      subtitle: 'Explore your previous health queries and insights',\r\n    },\r\n    '/settings': {\r\n      title: 'Settings ⚙️',\r\n      subtitle: 'Manage your profile and preferences',\r\n    },\r\n  };\r\n\r\n  const { title, subtitle } = bannerContent[location.pathname] || bannerContent['/'];\r\n\r\n  return (\r\n    <div className=\"banner-wrapper\">\r\n      <div className=\"banner\">\r\n        <h1 className=\"banner-title\">{title}</h1>\r\n        <p className=\"banner-subtitle\">{subtitle}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MedcodeBanner;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMM,aAAa,GAAG;IACpB,GAAG,EAAE;MACHC,KAAK,EAAE,eAAe;MACtBC,QAAQ,EAAE;IACZ,CAAC;IACD,gBAAgB,EAAE;MAChBD,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ,CAAC;IACD,WAAW,EAAE;MACXD,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE;IACZ;EACF,CAAC;EAED,MAAM;IAAED,KAAK;IAAEC;EAAS,CAAC,GAAGF,aAAa,CAACD,QAAQ,CAACI,QAAQ,CAAC,IAAIH,aAAa,CAAC,GAAG,CAAC;EAElF,oBACEJ,OAAA;IAAKQ,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BT,OAAA;MAAKQ,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrBT,OAAA;QAAIQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAEJ;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzCb,OAAA;QAAGQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEH;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA7BID,aAAa;EAAA,QACAH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,aAAa;AA+BnB,eAAeA,aAAa;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}