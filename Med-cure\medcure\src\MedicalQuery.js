import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from './contexts/AuthContext';
import apiService from './services/api';
import AuthModal from './components/AuthModal';
import './MedicalQuery.css';

const MedicalQuery = () => {
  const { user, isAuthenticated } = useAuth();
  const [inputMethod, setInputMethod] = useState('Text');
  const [isRecording, setIsRecording] = useState(false);
  const [query, setQuery] = useState('');
  const [imageFile, setImageFile] = useState(null);
  const [pdfFile, setPdfFile] = useState(null);
  const [response, setResponse] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState('Common User');
  const [selectedLanguage, setSelectedLanguage] = useState('English');

  const handleSubmit = useCallback(async () => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    if (!query && !imageFile && !pdfFile) {
      setError('Please provide a question or upload a medical image/document.');
      return;
    }

    setError('');
    setResponse('');
    setIsLoading(true);

    try {
      let result;

      if (imageFile || pdfFile) {
        // Use file upload endpoint
        const formData = new FormData();
        formData.append('user_id', user.id);
        formData.append('query', query || 'Please analyze this medical document/image');
        formData.append('role', user.role || selectedRole);
        formData.append('language', selectedLanguage);

        if (imageFile) {
          formData.append('file', imageFile);
        } else if (pdfFile) {
          formData.append('file', pdfFile);
        }

        result = await apiService.submitQueryWithFile(formData);
      } else {
        // Use text-only endpoint
        result = await apiService.submitQuery({
          user_id: user.id,
          query: query,
          role: user.role || selectedRole,
          language: selectedLanguage,
          has_image: false
        });
      }

      setResponse(result.response);
    } catch (err) {
      console.error('Query submission error:', err);
      setError(err.message || 'Failed to submit query. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [query, imageFile, pdfFile, isAuthenticated, user, selectedRole, selectedLanguage]);

  useEffect(() => {
    const rerun = localStorage.getItem('rerunQuery');
    if (rerun) {
      const parsed = JSON.parse(rerun);
      setQuery(parsed.query);
      setTimeout(() => {
        handleSubmit();
        localStorage.removeItem('rerunQuery');
      }, 500);
    }
  }, [handleSubmit]);

  const handleStartRecording = () => {
    setIsRecording(true);
    if ('webkitSpeechRecognition' in window) {
      const recognition = new window.webkitSpeechRecognition();
      recognition.lang = 'en-US';
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        setIsRecording(false);
      };
      recognition.onerror = () => {
        alert('Voice recognition error.');
        setIsRecording(false);
      };
      recognition.start();
    } else {
      alert('Voice recognition not supported in this browser.');
      setIsRecording(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const allowedImageTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    const allowedDocTypes = ['application/pdf'];

    if (allowedImageTypes.includes(file.type)) {
      setImageFile(file);
      setPdfFile(null);
      setError('');
    } else if (allowedDocTypes.includes(file.type)) {
      setPdfFile(file);
      setImageFile(null);
      setError('');
    } else {
      setImageFile(null);
      setPdfFile(null);
      setError('Only PNG, JPG, JPEG, and PDF files are allowed.');
    }
  };

  return (
    <div className="medical-query-wrapper">
      <h2>What would you like to know?</h2>

      {isAuthenticated && (
        <div className="user-info">
          <p>Welcome, <strong>{user.name}</strong> ({user.role})</p>
        </div>
      )}

      {/* Role and Language Selection for non-authenticated users */}
      {!isAuthenticated && (
        <div className="selection-section">
          <div className="form-group">
            <label>Role:</label>
            <select value={selectedRole} onChange={(e) => setSelectedRole(e.target.value)}>
              <option value="Common User">Common User</option>
              <option value="Student">Medical Student</option>
              <option value="Doctor">Doctor</option>
            </select>
          </div>
          <div className="form-group">
            <label>Language:</label>
            <select value={selectedLanguage} onChange={(e) => setSelectedLanguage(e.target.value)}>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Hindi">Hindi</option>
            </select>
          </div>
        </div>
      )}

      <p><strong>Input Method:</strong></p>

      <div className="method-toggle">
        <label className={inputMethod === 'Text' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Text"
            checked={inputMethod === 'Text'}
            onChange={() => setInputMethod('Text')}
          /> Text
        </label>
        <label className={inputMethod === 'Voice' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Voice"
            checked={inputMethod === 'Voice'}
            onChange={() => setInputMethod('Voice')}
          /> Voice
        </label>
      </div>

      {inputMethod === 'Text' ? (
        <textarea
          className="text-box"
          placeholder="Enter your medical query here..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      ) : (
        <div className="voice-box">
          <button onClick={handleStartRecording} className="start-btn">🎤 Start Recording</button>
          {isRecording && <p className="recording-text">🎙️ Listening...</p>}
        </div>
      )}

      <div className="upload-section">
        <div className="folder-icon">📁</div>
        <h3>Upload Medical Document</h3>
        <p>Upload X-rays, lab results, medical scans, PDFs, or any relevant documents</p>
        <input
          type="file"
          accept=".png,.jpg,.jpeg,.pdf"
          className="file-upload"
          onChange={handleFileChange}
          disabled={isLoading}
        />
        {imageFile && <p><strong>Selected Image:</strong> {imageFile.name}</p>}
        {pdfFile && <p><strong>Selected PDF:</strong> {pdfFile.name}</p>}
      </div>

      {error && <p className="error-message">{error}</p>}

      <button
        className="analyze-btn"
        onClick={handleSubmit}
        disabled={isLoading}
      >
        {isLoading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'}
      </button>

      {response && (
        <div className="response-box">
          <h3>Medical Analysis:</h3>
          <div dangerouslySetInnerHTML={{ __html: response.replace(/\n/g, '<br/>') }} />
        </div>
      )}

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default MedicalQuery;
