{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onSwitchToSignup,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user starts typing\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.username || !formData.password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const result = await login(formData.username, formData.password);\n      if (result.success) {\n        onClose(); // Close the login modal\n      } else {\n        setError(result.error || 'Login failed');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Welcome Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Sign in to your MedCure account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"username\",\n          name: \"username\",\n          value: formData.username,\n          onChange: handleChange,\n          placeholder: \"Enter your username\",\n          disabled: isLoading,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          id: \"password\",\n          name: \"password\",\n          value: formData.password,\n          onChange: handleChange,\n          placeholder: \"Enter your password\",\n          disabled: isLoading,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"auth-button\",\n        disabled: isLoading,\n        children: isLoading ? 'Signing In...' : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"link-button\",\n          onClick: onSwitchToSignup,\n          disabled: isLoading,\n          children: \"Sign up here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"pROAzXj7UZDQ2aK9mDLAmtyJXBw=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onSwitchToSignup", "onClose", "_s", "formData", "setFormData", "username", "password", "isLoading", "setIsLoading", "error", "setError", "login", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "required", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport './Auth.css';\n\nconst Login = ({ onSwitchToSignup, onClose }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { login } = useAuth();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user starts typing\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username || !formData.password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const result = await login(formData.username, formData.password);\n      \n      if (result.success) {\n        onClose(); // Close the login modal\n      } else {\n        setError(result.error || 'Login failed');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-header\">\n        <h2>Welcome Back</h2>\n        <p>Sign in to your MedCure account</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"auth-form\">\n        <div className=\"form-group\">\n          <label htmlFor=\"username\">Username</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            name=\"username\"\n            value={formData.username}\n            onChange={handleChange}\n            placeholder=\"Enter your username\"\n            disabled={isLoading}\n            required\n          />\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            placeholder=\"Enter your password\"\n            disabled={isLoading}\n            required\n          />\n        </div>\n\n        {error && <div className=\"error-message\">{error}</div>}\n\n        <button \n          type=\"submit\" \n          className=\"auth-button\"\n          disabled={isLoading}\n        >\n          {isLoading ? 'Signing In...' : 'Sign In'}\n        </button>\n      </form>\n\n      <div className=\"auth-footer\">\n        <p>\n          Don't have an account?{' '}\n          <button \n            type=\"button\" \n            className=\"link-button\"\n            onClick={onSwitchToSignup}\n            disabled={isLoading}\n          >\n            Sign up here\n          </button>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,KAAK,GAAGA,CAAC;EAAEC,gBAAgB;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEgB;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE3B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFN,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAACf,QAAQ,CAACE,QAAQ,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC5CI,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMS,MAAM,GAAG,MAAMR,KAAK,CAACR,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAEhE,IAAIa,MAAM,CAACC,OAAO,EAAE;QAClBnB,OAAO,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,MAAM;QACLS,QAAQ,CAACS,MAAM,CAACV,KAAK,IAAI,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZX,QAAQ,CAAC,iDAAiD,CAAC;IAC7D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKwB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BzB,OAAA;MAAKwB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzB,OAAA;QAAAyB,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB7B,OAAA;QAAAyB,QAAA,EAAG;MAA+B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAEN7B,OAAA;MAAM8B,QAAQ,EAAEX,YAAa;MAACK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACjDzB,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzB,OAAA;UAAO+B,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C7B,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACbhB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEb,QAAQ,CAACE,QAAS;UACzB2B,QAAQ,EAAEpB,YAAa;UACvBqB,WAAW,EAAC,qBAAqB;UACjCC,QAAQ,EAAE3B,SAAU;UACpB4B,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzB,OAAA;UAAO+B,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C7B,OAAA;UACEgC,IAAI,EAAC,UAAU;UACfC,EAAE,EAAC,UAAU;UACbhB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;UACzB0B,QAAQ,EAAEpB,YAAa;UACvBqB,WAAW,EAAC,qBAAqB;UACjCC,QAAQ,EAAE3B,SAAU;UACpB4B,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELlB,KAAK,iBAAIX,OAAA;QAAKwB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEd;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtD7B,OAAA;QACEgC,IAAI,EAAC,QAAQ;QACbR,SAAS,EAAC,aAAa;QACvBY,QAAQ,EAAE3B,SAAU;QAAAgB,QAAA,EAEnBhB,SAAS,GAAG,eAAe,GAAG;MAAS;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEP7B,OAAA;MAAKwB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BzB,OAAA;QAAAyB,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BzB,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,aAAa;UACvBc,OAAO,EAAEpC,gBAAiB;UAC1BkC,QAAQ,EAAE3B,SAAU;UAAAgB,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CA1GIH,KAAK;EAAA,QAQSH,OAAO;AAAA;AAAAyC,EAAA,GARrBtC,KAAK;AA4GX,eAAeA,KAAK;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}