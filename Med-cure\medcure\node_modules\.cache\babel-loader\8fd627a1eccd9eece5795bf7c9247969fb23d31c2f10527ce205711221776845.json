{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Med-cure\\\\Med-cure\\\\medcure\\\\src\\\\QueryHistory.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Footer from './Footer';\nimport './QueryHistory.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QueryHistory = () => {\n  _s();\n  const [queries, setQueries] = useState([]);\n  useEffect(() => {\n    const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\n    setQueries(savedQueries);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"query-history-container\",\n    children: [queries.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-message\",\n      children: [\"You haven't made any queries yet. \", /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/dashboard\",\n        children: \"Go to the Dashboard to start!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 45\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this) : queries.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"query-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"query-header\",\n        children: [\"Query: \", q.query]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"query-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Query:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 18\n          }, this), \" \", q.query]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Role:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 18\n          }, this), \" \", q.role]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 18\n          }, this), \" \", q.time]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"rerun-btn\",\n          onClick: () => {\n            localStorage.setItem('rerunQuery', JSON.stringify(q));\n            window.location.href = '/dashboard'; // redirect to MedicalQuery\n          },\n          children: \"RERUN THIS QUERY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 13\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 11\n    }, this)), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(QueryHistory, \"8ufg30FGvL92hxlNSdQiwIqkIYM=\");\n_c = QueryHistory;\nexport default QueryHistory;\nvar _c;\n$RefreshReg$(_c, \"QueryHistory\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Footer", "jsxDEV", "_jsxDEV", "QueryHistory", "_s", "queries", "setQueries", "savedQueries", "JSON", "parse", "localStorage", "getItem", "className", "children", "length", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "q", "index", "query", "role", "time", "onClick", "setItem", "stringify", "window", "location", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/src/QueryHistory.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport Footer from './Footer';\r\nimport './QueryHistory.css';\r\n\r\nconst QueryHistory = () => {\r\n  const [queries, setQueries] = useState([]);\r\n\r\n  useEffect(() => {\r\n    const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\r\n    setQueries(savedQueries);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"query-history-container\">\r\n      {queries.length === 0 ? (\r\n        <div className=\"empty-message\">\r\n          You haven't made any queries yet. <a href=\"/dashboard\">Go to the Dashboard to start!</a>\r\n        </div>\r\n      ) : (\r\n        queries.map((q, index) => (\r\n          <div key={index} className=\"query-card\">\r\n            <div className=\"query-header\">Query: {q.query}</div>\r\n            <div className=\"query-body\">\r\n              <p><strong>Query:</strong> {q.query}</p>\r\n              <p><strong>Role:</strong> {q.role}</p>\r\n              <p><strong>Time:</strong> {q.time}</p>\r\n              <button\r\n                className=\"rerun-btn\"\r\n                onClick={() => {\r\n                  localStorage.setItem('rerunQuery', JSON.stringify(q));\r\n                  window.location.href = '/dashboard'; // redirect to MedicalQuery\r\n                }}\r\n              >\r\n                RERUN THIS QUERY\r\n              </button>\r\n\r\n            </div>\r\n          </div>\r\n        ))\r\n      )}\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QueryHistory;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAE1CD,SAAS,CAAC,MAAM;IACd,MAAMS,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;IAC7EL,UAAU,CAACC,YAAY,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA;IAAKU,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACrCR,OAAO,CAACS,MAAM,KAAK,CAAC,gBACnBZ,OAAA;MAAKU,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,oCACK,eAAAX,OAAA;QAAGa,IAAI,EAAC,YAAY;QAAAF,QAAA,EAAC;MAA6B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,GAENd,OAAO,CAACe,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACnBpB,OAAA;MAAiBU,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACrCX,OAAA;QAAKU,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,SAAO,EAACQ,CAAC,CAACE,KAAK;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpDjB,OAAA;QAAKU,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBX,OAAA;UAAAW,QAAA,gBAAGX,OAAA;YAAAW,QAAA,EAAQ;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACE,CAAC,CAACE,KAAK;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCjB,OAAA;UAAAW,QAAA,gBAAGX,OAAA;YAAAW,QAAA,EAAQ;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACE,CAAC,CAACG,IAAI;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCjB,OAAA;UAAAW,QAAA,gBAAGX,OAAA;YAAAW,QAAA,EAAQ;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACE,CAAC,CAACI,IAAI;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCjB,OAAA;UACEU,SAAS,EAAC,WAAW;UACrBc,OAAO,EAAEA,CAAA,KAAM;YACbhB,YAAY,CAACiB,OAAO,CAAC,YAAY,EAAEnB,IAAI,CAACoB,SAAS,CAACP,CAAC,CAAC,CAAC;YACrDQ,MAAM,CAACC,QAAQ,CAACf,IAAI,GAAG,YAAY,CAAC,CAAC;UACvC,CAAE;UAAAF,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC;IAAA,GAhBEG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiBV,CACN,CACF,eACDjB,OAAA,CAACF,MAAM;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACf,EAAA,CAvCID,YAAY;AAAA4B,EAAA,GAAZ5B,YAAY;AAyClB,eAAeA,YAAY;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}