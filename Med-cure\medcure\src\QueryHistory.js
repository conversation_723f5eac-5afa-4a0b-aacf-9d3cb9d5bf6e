import React, { useEffect, useState } from 'react';
import { useAuth } from './contexts/AuthContext';
import apiService from './services/api';
import AuthModal from './components/AuthModal';
import Footer from './Footer';
import './QueryHistory.css';

const QueryHistory = () => {
  const { user, isAuthenticated } = useAuth();
  const [queries, setQueries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAuthModal, setShowAuthModal] = useState(false);

  useEffect(() => {
    const fetchQueryHistory = async () => {
      if (!isAuthenticated) {
        setShowAuthModal(true);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError('');
        const response = await apiService.getQueryHistory(user.id, 50);
        setQueries(response.history || []);
      } catch (err) {
        console.error('Error fetching query history:', err);
        setError('Failed to load query history. Please try again.');
        // Fallback to localStorage for backward compatibility
        const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
        setQueries(savedQueries);
      } finally {
        setLoading(false);
      }
    };

    fetchQueryHistory();
  }, [isAuthenticated, user]);

  const handleRerunQuery = (query) => {
    localStorage.setItem('rerunQuery', JSON.stringify({
      query: query.query,
      role: query.role
    }));
    window.location.href = '/'; // redirect to main dashboard
  };

  const formatDate = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (err) {
      return timestamp; // fallback to original string
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="query-history-container">
        <div className="auth-required-message">
          <h2>Authentication Required</h2>
          <p>Please sign in to view your query history.</p>
          <button
            className="auth-btn"
            onClick={() => setShowAuthModal(true)}
          >
            Sign In
          </button>
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  return (
    <div className="query-history-container">
      <div className="history-header">
        <h2>Query History</h2>
        <p>Your recent medical queries and analyses</p>
      </div>

      {loading ? (
        <div className="loading-message">
          <p>Loading your query history...</p>
        </div>
      ) : error ? (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      ) : queries.length === 0 ? (
        <div className="empty-message">
          <h3>No queries yet</h3>
          <p>You haven't made any medical queries yet.</p>
          <a href="/" className="dashboard-link">Go to Dashboard to start!</a>
        </div>
      ) : (
        <div className="queries-list">
          {queries.map((q, index) => (
            <div key={q.id || index} className="query-card">
              <div className="query-header">
                <span className="query-title">
                  {q.query.length > 100 ? `${q.query.substring(0, 100)}...` : q.query}
                </span>
                {q.has_image && <span className="image-indicator">📷</span>}
              </div>
              <div className="query-body">
                <div className="query-details">
                  <p><strong>Query:</strong> {q.query}</p>
                  <p><strong>Role:</strong> {q.role}</p>
                  <p><strong>Date:</strong> {formatDate(q.timestamp || q.time)}</p>
                  {q.has_image && <p><strong>Included:</strong> Medical image/document</p>}
                </div>
                <div className="query-actions">
                  <button
                    className="rerun-btn"
                    onClick={() => handleRerunQuery(q)}
                  >
                    🔄 Rerun Query
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <Footer />
    </div>
  );
};

export default QueryHistory;
