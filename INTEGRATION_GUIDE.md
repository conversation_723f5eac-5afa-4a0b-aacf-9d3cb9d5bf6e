# Med-cure Backend & Frontend Integration Guide

This guide provides comprehensive instructions for setting up and running the integrated Med-cure application with FastAPI backend and React frontend.

## 🏗️ Architecture Overview

- **Backend**: FastAPI-based Python server with medical AI capabilities
- **Frontend**: React application with Material-UI components
- **Database**: PostgreSQL for user management and query history
- **AI Integration**: Google Generative AI (Gemini 2.0) for medical analysis
- **Authentication**: JWT-like session management
- **File Processing**: Support for images (PNG, JPG, JPEG) and PDFs

## 📋 Prerequisites

- Python 3.8+ 
- Node.js 16+ and npm/yarn
- PostgreSQL database
- Google AI API key

## 🚀 Quick Start

### 1. Backend Setup

```bash
# Navigate to backend directory
cd Backend/

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the server
python app.py
```

The backend will be available at `http://localhost:8000`

### 2. Frontend Setup

```bash
# Navigate to frontend directory
cd Med-cure/medcure/

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env if needed (default API URL is http://localhost:8000)

# Start development server
npm start
```

The frontend will be available at `http://localhost:3000`

## 🔧 Configuration

### Backend Environment Variables (.env)

```env
# Environment
ENVIRONMENT=development

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/medcure_db

# Google AI
GOOGLE_API_KEY=your_google_api_key_here

# Frontend URLs (for production CORS)
FRONTEND_URLS=https://yourdomain.com,https://www.yourdomain.com

# Server
HOST=0.0.0.0
PORT=8000
```

### Frontend Environment Variables (.env)

```env
# Backend API URL
REACT_APP_API_URL=http://localhost:8000

# Environment
REACT_APP_ENV=development

# Debug mode
REACT_APP_DEBUG=true
```

## 📡 API Endpoints

### Authentication
- `POST /login` - User login
- `POST /signup` - User registration

### Medical Queries
- `POST /query` - Submit text-based medical query
- `POST /query-with-file` - Submit query with image/PDF
- `GET /history/{user_id}` - Get user's query history

### User Management
- `GET /profile/{user_id}` - Get user profile
- `PUT /profile` - Update user profile
- `PUT /change-password` - Change user password

### Utilities
- `POST /generate-pdf` - Generate medical report PDF
- `GET /user-stats/{user_id}` - Get user statistics

## 🔐 Authentication Flow

1. User signs up or logs in through the frontend
2. Backend validates credentials and returns user data
3. Frontend stores user session in localStorage
4. AuthContext manages authentication state across components
5. Protected routes require authentication

## 📁 File Structure

```
Med-cure/
├── Backend/
│   ├── app.py              # FastAPI application
│   ├── requirements.txt    # Python dependencies
│   ├── .env.example       # Environment template
│   └── uploads/           # File upload directory
├── Med-cure/medcure/
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── contexts/      # React contexts
│   │   ├── services/      # API service layer
│   │   └── ...
│   ├── package.json       # Node.js dependencies
│   └── .env.example      # Environment template
└── INTEGRATION_GUIDE.md  # This file
```

## 🧪 Testing the Integration

1. Start both backend and frontend servers
2. Navigate to `http://localhost:3000`
3. Try the following features:
   - Sign up for a new account
   - Log in with existing credentials
   - Submit a medical query (text-only)
   - Upload and analyze a medical image/PDF
   - View query history
   - Update profile settings
   - Change password

## 🚀 Production Deployment

### Backend Deployment

1. Set `ENVIRONMENT=production` in .env
2. Configure production database URL
3. Set specific frontend URLs in `FRONTEND_URLS`
4. Use a production WSGI server like Gunicorn:

```bash
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app --bind 0.0.0.0:8000
```

### Frontend Deployment

1. Build the production bundle:

```bash
npm run build
```

2. Serve the build directory with a web server (nginx, Apache, etc.)
3. Update `REACT_APP_API_URL` to point to production backend

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend CORS is configured for your frontend URL
2. **API Connection Failed**: Check if backend is running and accessible
3. **Authentication Issues**: Verify user credentials and session storage
4. **File Upload Errors**: Check file size limits and supported formats
5. **Database Connection**: Ensure PostgreSQL is running and accessible

### Debug Mode

Enable debug mode in frontend `.env`:
```env
REACT_APP_DEBUG=true
```

This will show additional console logs for API calls and errors.

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [Google AI API Documentation](https://ai.google.dev/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🛠️ Development Scripts

### Setup Script (setup.sh)

```bash
#!/bin/bash
echo "Setting up Med-cure application..."

# Backend setup
echo "Setting up backend..."
cd Backend/
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env
echo "Backend setup complete. Please edit Backend/.env with your configuration."

# Frontend setup
echo "Setting up frontend..."
cd ../Med-cure/medcure/
npm install
cp .env.example .env
echo "Frontend setup complete."

echo "Setup finished! Run 'npm run dev' to start both servers."
```

### Development Start Script (package.json)

Add to `Med-cure/medcure/package.json`:

```json
{
  "scripts": {
    "dev": "concurrently \"npm run backend\" \"npm start\"",
    "backend": "cd ../../Backend && python app.py",
    "start": "react-scripts start",
    "build": "react-scripts build"
  },
  "devDependencies": {
    "concurrently": "^7.6.0"
  }
}
```

## 🔄 API Integration Details

### Error Handling

The frontend API service includes comprehensive error handling:

```javascript
// Example error handling in API service
async handleResponse(response) {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }
  return response.json();
}
```

### Authentication Context

The AuthContext provides:
- User state management
- Login/logout functionality
- Profile updates
- Password changes
- Session persistence

### File Upload Support

Supported file types:
- Images: PNG, JPG, JPEG
- Documents: PDF
- Maximum file size: 10MB (configurable)

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review console logs in browser developer tools
3. Check backend logs for API errors
4. Ensure all environment variables are properly configured
5. Verify database connection and Google AI API key
