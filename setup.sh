#!/bin/bash

echo "🚀 Setting up Med-cure application..."
echo "======================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ and try again."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Backend setup
echo "🐍 Setting up backend..."
cd Backend/

# Create virtual environment
echo "Creating Python virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created Backend/.env file"
else
    echo "⚠️  Backend/.env already exists"
fi

echo "✅ Backend setup complete!"

# Frontend setup
echo ""
echo "⚛️  Setting up frontend..."
cd ../Med-cure/medcure/

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created frontend .env file"
else
    echo "⚠️  Frontend .env already exists"
fi

echo "✅ Frontend setup complete!"

# Final instructions
echo ""
echo "🎉 Setup finished successfully!"
echo "=============================="
echo ""
echo "📝 Next steps:"
echo "1. Edit Backend/.env with your configuration:"
echo "   - Set your Google AI API key"
echo "   - Configure your PostgreSQL database URL"
echo ""
echo "2. Edit Med-cure/medcure/.env if needed:"
echo "   - Default backend URL is http://localhost:8000"
echo ""
echo "3. Start the application:"
echo "   Backend:  cd Backend && python app.py"
echo "   Frontend: cd Med-cure/medcure && npm start"
echo ""
echo "🌐 The application will be available at:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📚 See INTEGRATION_GUIDE.md for detailed instructions"
