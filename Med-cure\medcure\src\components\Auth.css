/* Authentication Components Styles */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.auth-modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
}

.auth-close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  z-index: 1001;
}

.auth-close-button:hover {
  color: #333;
}

.auth-container {
  padding: 40px 30px 30px;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.auth-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 6px;
  color: #2c3e50;
  font-weight: 500;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  background-color: #fff;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.7;
}

.auth-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.auth-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.auth-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.auth-footer {
  text-align: center;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

.auth-footer p {
  color: #7f8c8d;
  margin: 0;
}

.link-button {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  padding: 0;
}

.link-button:hover:not(:disabled) {
  color: #2980b9;
}

.link-button:disabled {
  color: #bdc3c7;
  cursor: not-allowed;
}

.error-message {
  background-color: #fee;
  color: #c0392b;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #e74c3c;
  font-size: 14px;
  margin: 10px 0;
}

.success-message {
  background-color: #eef;
  color: #27ae60;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #2ecc71;
  font-size: 14px;
  margin: 10px 0;
}

/* Responsive design */
@media (max-width: 480px) {
  .auth-modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .auth-container {
    padding: 30px 20px 20px;
  }
  
  .auth-header h2 {
    font-size: 24px;
  }
  
  .form-group input,
  .form-group select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
