{"ast": null, "code": "import prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme({\n  colorSchemeSelector = `[${DEFAULT_ATTRIBUTE}=\"%s\"]`,\n  ...theme\n}) {\n  const output = theme;\n  const result = prepareCssVars(output, {\n    ...theme,\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  });\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;", "map": {"version": 3, "names": ["prepareCssVars", "createGetColorSchemeSelector", "DEFAULT_ATTRIBUTE", "createCssVarsTheme", "colorSchemeSelector", "theme", "output", "result", "prefix", "cssVarPrefix", "vars", "generateThemeVars", "generateStyleSheets", "getColorSchemeSelector"], "sources": ["C:/Users/<USER>/Downloads/Med-cure/Med-cure/medcure/node_modules/@mui/system/esm/cssVars/createCssVarsTheme.js"], "sourcesContent": ["import prepareCssVars from \"./prepareCssVars.js\";\nimport { createGetColorSchemeSelector } from \"./getColorSchemeSelector.js\";\nimport { DEFAULT_ATTRIBUTE } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nfunction createCssVarsTheme({\n  colorSchemeSelector = `[${DEFAULT_ATTRIBUTE}=\"%s\"]`,\n  ...theme\n}) {\n  const output = theme;\n  const result = prepareCssVars(output, {\n    ...theme,\n    prefix: theme.cssVarPrefix,\n    colorSchemeSelector\n  });\n  output.vars = result.vars;\n  output.generateThemeVars = result.generateThemeVars;\n  output.generateStyleSheets = result.generateStyleSheets;\n  output.colorSchemeSelector = colorSchemeSelector;\n  output.getColorSchemeSelector = createGetColorSchemeSelector(colorSchemeSelector);\n  return output;\n}\nexport default createCssVarsTheme;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,4BAA4B,QAAQ,6BAA6B;AAC1E,SAASC,iBAAiB,QAAQ,mDAAmD;AACrF,SAASC,kBAAkBA,CAAC;EAC1BC,mBAAmB,GAAG,IAAIF,iBAAiB,QAAQ;EACnD,GAAGG;AACL,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGD,KAAK;EACpB,MAAME,MAAM,GAAGP,cAAc,CAACM,MAAM,EAAE;IACpC,GAAGD,KAAK;IACRG,MAAM,EAAEH,KAAK,CAACI,YAAY;IAC1BL;EACF,CAAC,CAAC;EACFE,MAAM,CAACI,IAAI,GAAGH,MAAM,CAACG,IAAI;EACzBJ,MAAM,CAACK,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB;EACnDL,MAAM,CAACM,mBAAmB,GAAGL,MAAM,CAACK,mBAAmB;EACvDN,MAAM,CAACF,mBAAmB,GAAGA,mBAAmB;EAChDE,MAAM,CAACO,sBAAsB,GAAGZ,4BAA4B,CAACG,mBAAmB,CAAC;EACjF,OAAOE,MAAM;AACf;AACA,eAAeH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}